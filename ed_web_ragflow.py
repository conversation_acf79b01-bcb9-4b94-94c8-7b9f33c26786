import requests
import streamlit as st
from streamlit_float import *
import streamlit.components.v1 as components
import json
import time
import cv2
import secrets
import random
from datetime import datetime
import os
import base64
import logging
from io import BytesIO
import pdfkit
from markdown import markdown
import tempfile
from getDocId import upload_or_get_doc_id
from getAnswer import create_session
import urllib3
from streamlit.config import  set_option
import yaml
import bcrypt
import random
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import smtplib
from web.blog_page import reset_user_counts
from logger_config import video_server_logger as logger
from tool.weather.get_weather import get_weather
from tool.weather.weather_display import render_weather_html
# 禁用警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
#blog id=5252db8a8d5311efab1e0242ac120005
# chat id=1477989a766611efac910242ac120006
# test id=8a3a5cfa71e511efb3a40242ac120005
# DM  id=2fbe8d6e6e1111ef9b760242ac120002
dataset_id= "a3870650ffa111ef92aa2a5c03e306d6" #视频知识库
base_url = "http://localhost:8080/v1/api"
# api_key_model_2 = "ragflow-M4ZTVhY2Q0YWIyZTExZWY4ZDlhMDI0Mm"  # 模型DM
api_key_model_1 = "ragflow-Y1NTkyNDBlZmZhNTExZWY5ZmNhZWUzZD"  # 2DNM
# api_key_chat="ragflow-M4ZTVhY2Q0YWIyZTExZWY4ZDlhMDI0Mm" #chat生成模型
api_key_model=api_key_model_1   #上传知识库
api_key_deepseek = "***********************************"#deepseeks
api_key_siliconflow = "sk-izwfqiufrwdqfkvqzklcyaashpmraqaaosuchkjnettqpabb"  #illconflow
api_key_qwen="sk-61010d52e30c4fb096d240ad7fae39df"
chat_id="3b58bf3e8e2611ef98110242ac120006" #blog_chat
chat_id_test="88a31ba86e1011efa2e10242ac120002" #test_chat
# 定义访问量文件路径
visit_count_file = 'visit_count.json'
# 设置配置文件路径
# config_path = 'config.yaml'
st.set_page_config(
    page_title="AI助教",
    page_icon="🧊",
    layout="wide",
    initial_sidebar_state="expanded",
)
# 上传最大文件
set_option('server.maxUploadSize', 1024)

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# 指定 wkhtmltopdf 的路径
WKHTMLTOPDF_PATH = r'C:\Program Files\wkhtmltopdf\bin\wkhtmltopdf.exe'
configpdf = pdfkit.configuration(wkhtmltopdf=WKHTMLTOPDF_PATH)
# 定义允许的文件类型
ALLOWED_EXTENSIONS = ['.docx', '.xlsx', '.pptx', '.jpg', '.jpeg', '.png', '.pdf', '.txt', '.md', '.json', '.eml']
# 定义用户文件记录的 JSON 文件路径
USER_FILES_JSON = os.path.join(SCRIPT_DIR, "user", "user_files.json")
RAGDOC_DIR = os.path.join(SCRIPT_DIR, "ragdoc")
DOC_IDS_JSON = os.path.join(SCRIPT_DIR, "ragdoc", "doc_ids.json")
VIDEO_BASE_PATH = os.path.join(SCRIPT_DIR, "video")
PDF_BASE_PATH=os.path.join(SCRIPT_DIR, "doc")
RESET_PERFORMED = False#初始化每次次数限制参数
USER_RAG_COUNTS_FILE = os.path.join(SCRIPT_DIR, 'user_rag_counts.json')

max_rag_uploads = 2  # 确保这个值与 manage_user_rag_count 函数中的 max_uploads 一致
max_video_uploads = 1
vip_extra_rag_uploads = 10
vip_extra_video_uploads = 10

# float_init()



# 预设美观大方的颜色组合
color_schemes = [
    {
        'gradient': ['#2c3e50', '#3498db'],
        'text': '#ffffff'
    },
    {
        'gradient': ['#16a085', '#f4d03f'],
        'text': '#ffffff'
    },
    {
        'gradient': ['#603813', '#b29f94'],
        'text': '#ffffff'
    },
    {
        'gradient': ['#4b6cb7', '#182848'],
        'text': '#ffffff'
    },
    {
        'gradient': ['#3494e6', '#ec6ead'],
        'text': '#ffffff'
    },
    {
        'gradient': ['#614385', '#516395'],
        'text': '#ffffff'
    },
    {
        'gradient': ['#1f4037', '#99f2c8'],
        'text': '#ffffff'
    },
    {
        'gradient': ['#c31432', '#240b36'],
        'text': '#ffffff'
    }
]

def get_random_color_scheme():
    return random.choice(color_schemes)

# 新增：根据背景色自动生成反色字体
def get_contrast_color(hex_color):
    hex_color = hex_color.lstrip('#')
    r, g, b = [int(hex_color[i:i+2], 16) for i in (0, 2, 4)]
    brightness = (r * 299 + g * 587 + b * 114) / 1000
    return '#000000' if brightness > 186 else '#ffffff'






###################################################################################
# vip限制
###################################################################################

def get_user_upload_limits(username):
    """获取用户的上传限制次数，考虑 VIP 状态"""
    # 基础限制
    limits = {
        'rag': max_rag_uploads,
        'video': max_video_uploads
    }
    
    # 检查 VIP 状态
    try:
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            
        if username == "admin":
            # 管理员无限制
            return {
                'rag': 999999,  # 使用大数字代替 float('inf')
                'video': 999999
            }
            
        if username in config['credentials']['usernames']:
            user_info = config['credentials']['usernames'][username]
            if 'vip_expiry' in user_info:
                expiry_timestamp = user_info['vip_expiry']
                if expiry_timestamp > time.time():  # VIP 未过期
                    # VIP 用户获得额外 10 次上传机会
                    limits['rag'] += vip_extra_rag_uploads
                    limits['video'] += vip_extra_video_uploads
    except Exception as e:
        logging.error(f"Error checking VIP status for upload limits: {str(e)}")
    
    return limits

###################################################################################
# 用户rag配置
###################################################################################
# 假设这是存储用户配置的文件路径
USER_CONFIG_FILE = 'user_configs.json'

def save_user_config(username, config):
    # 如果文件不存在，创建一个空的字典
    if not os.path.exists(USER_CONFIG_FILE):
        with open(USER_CONFIG_FILE, 'w') as f:
            json.dump({}, f)
    # 读取现有的配置
    with open(USER_CONFIG_FILE, 'r', encoding='utf-8') as f:
        all_configs = json.load(f)
    # 更新用户的配置，保留未更新的参数
    if username not in all_configs:
        all_configs[username] = {}
    all_configs[username].update(config)  # 只更新提供的参数
    # 写回文件
    with open(USER_CONFIG_FILE, 'w', encoding='utf-8') as f:
        json.dump(all_configs, f, indent=4)

def initialize_user_config():
    # 初始化用户配置为空值
    return {
        'method': None,
        'net_method': None,
        'query_source': None,
        'api_key_deepseek': None,
        'language': None,
        'summary_length': None,
        'selected_doc_ids': None,
        'conversation_id': None,
        'api_key_rag': None,
        'query_length': None
    }
#############################################################################
# 获取api_key
#############################################################################
def get_api_key():
    # 初始化 session_state
    if 'api_key_type' not in st.session_state:
        st.session_state.api_key_type = ""
    if 'api_key_deepseek' not in st.session_state:
        st.session_state.api_key_deepseek = ""
    if 'api_key_siliconflow' not in st.session_state:
        st.session_state.api_key_siliconflow = ""
    if 'api_key_qwen' not in st.session_state:
        st.session_state.api_key_qwen = ""

    api_key_type="deepseek"
    if not st.session_state.api_key_type:
        if 'username' in st.session_state:
            username = st.session_state.username
            try:
                with open('user_configs.json', 'r', encoding='utf-8') as f:
                    user_configs = json.load(f)
                    if username in user_configs:
                        user_config = user_configs[username]
                        if 'api_key_type' in user_config and user_config['api_key_type']:
                            st.session_state.api_key_type = user_config['api_key_type']
                            api_key_type=user_config['api_key_type']

                            if 'api_key_deepseek' in user_config and user_config['api_key_deepseek']:
                                st.session_state.api_key_deepseek = user_config['api_key_deepseek']
                            else:
                                st.session_state.api_key_deepseek = ""

                            if 'api_key_qwen' in user_config and user_config['api_key_qwen']:
                                st.session_state.api_key_qwen = user_config['api_key_qwen']
                            else:
                                st.session_state.api_key_qwen = ""

                            if 'api_key_siliconflow' in user_config and user_config['api_key_siliconflow']:
                                st.session_state.api_key_siliconflow = user_config['api_key_siliconflow']
                            else:
                                st.session_state.api_key_siliconflow = ""
                        else:
                            st.session_state.api_key_type = "deepseek"
                            api_key_type="deepseek"

            except Exception as e:
                logging.error(f"Error checking VIP status: {str(e)}")


    # 检查是否是管理员或 VIP 用户
    is_privileged_user = False
    if 'username' in st.session_state:
        username = st.session_state.username       
        # 检查管理员身份
        if username == "admin":
            is_privileged_user = True
        else:
            # 检查 VIP 状态
            try:
                with open('config.yaml', 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    
                if username in config['credentials']['usernames']:
                    user_info = config['credentials']['usernames'][username]
                    if 'vip_expiry' in user_info:
                        expiry_timestamp = user_info['vip_expiry']
                        if expiry_timestamp > time.time():  # VIP 未过期
                            is_privileged_user = True
            except Exception as e:
                logging.error(f"Error checking VIP status: {str(e)}")



    # 只有当两个 API key 都为空时才从配置文件加载
    if api_key_type and api_key_type != st.session_state.api_key_type:
        st.session_state.api_key_type = api_key_type
        if not st.session_state.api_key_deepseek or not st.session_state.api_key_siliconflow or not st.session_state.api_key_qwen:
            if 'username' in st.session_state:
                username = st.session_state.username
                try:
                    with open('user_configs.json', 'r', encoding='utf-8') as f:
                        user_configs = json.load(f)
                        if username in user_configs:
                            user_config = user_configs[username]
                            # 如果存在deepseek密钥，优先使用deepseek
                            if 'api_key_deepseek' in user_config and user_config['api_key_deepseek']:
                                st.session_state.api_key_deepseek = user_config['api_key_deepseek']
                                if 'api_key_siliconflow' in user_config and user_config['api_key_siliconflow']:
                                    st.session_state.api_key_siliconflow =user_config['api_key_siliconflow']
                                if 'api_key_qwen' in user_config and user_config['api_key_qwen']:
                                    st.session_state.api_key_qwen = user_config['api_key_qwen']
                            # 否则使用siliconflow密钥
                            elif 'api_key_siliconflow' in user_config and user_config['api_key_siliconflow']:
                                st.session_state.api_key_siliconflow = user_config['api_key_siliconflow']
                                if 'api_key_qwen' in user_config and user_config['api_key_qwen']:   
                                    st.session_state.api_key_qwen = user_config['api_key_qwen']
                            elif 'api_key_qwen' in user_config and user_config['api_key_qwen']:
                                st.session_state.api_key_qwen = user_config['api_key_qwen']
                except Exception as e:
                    logging.error(f"Error loading user config: {str(e)}")

    # 添加下拉框选择 API key 类型
    api_key_type = st.sidebar.selectbox(
        "选择 API 密钥类型",
        options=["siliconflow", "deepseek", "qwen"],
        index=0 if st.session_state.api_key_type == "siliconflow" 
              else 1 if st.session_state.api_key_type == "deepseek"
              else 2
    )
    


    # 只有当两个 API key 都为空时才从配置文件加载
    if api_key_type and api_key_type != st.session_state.api_key_type:
        st.session_state.api_key_type = api_key_type
        if not st.session_state.api_key_deepseek or not st.session_state.api_key_siliconflow or not st.session_state.api_key_qwen:
            if 'username' in st.session_state:
                username = st.session_state.username
                try:
                    with open('user_configs.json', 'r', encoding='utf-8') as f:
                        user_configs = json.load(f)
                        if username in user_configs:
                            user_config = user_configs[username]
                            # 如果存在deepseek密钥，优先使用deepseek
                            if 'api_key_deepseek' in user_config and user_config['api_key_deepseek']:
                                st.session_state.api_key_deepseek = user_config['api_key_deepseek']
                                if 'api_key_siliconflow' in user_config and user_config['api_key_siliconflow']:
                                    st.session_state.api_key_siliconflow =user_config['api_key_siliconflow']
                                if 'api_key_qwen' in user_config and user_config['api_key_qwen']:
                                    st.session_state.api_key_qwen = user_config['api_key_qwen']
                            # 否则使用siliconflow密钥
                            elif 'api_key_siliconflow' in user_config and user_config['api_key_siliconflow']:
                                st.session_state.api_key_siliconflow = user_config['api_key_siliconflow']
                                if 'api_key_qwen' in user_config and user_config['api_key_qwen']:   
                                    st.session_state.api_key_qwen = user_config['api_key_qwen']
                            elif 'api_key_qwen' in user_config and user_config['api_key_qwen']:
                                st.session_state.api_key_qwen = user_config['api_key_qwen']
                except Exception as e:
                    logging.error(f"Error loading user config: {str(e)}")





    # 管理员或VIP用户使用预设的API密钥
    if is_privileged_user:
        if api_key_type == "deepseek":
            return api_key_deepseek, "deepseek"
        elif api_key_type == "qwen":
            return api_key_qwen, "qwen"
        else:
            return api_key_siliconflow, "siliconflow"

    # 非特权用户需要输入API密钥
    # 添加自定义CSS样式
    st.markdown("""
        <style>
        .api-links-container {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            margin: 10px 0;
        }
        .api-link-title {
            color: #495057;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .api-link-button {
            display: block;
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 8px 12px;
            margin: 8px 0;
            color: #0366d6;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .api-link-button:hover {
            background-color: #f1f8ff;
            border-color: #0366d6;
            text-decoration: none;
        }
        .api-link-icon {
            margin-right: 8px;
            font-size: 16px;
        }
        </style>
    """, unsafe_allow_html=True)

    # 添加美化后的 API 密钥申请链接
    st.sidebar.markdown("""
    <div class="api-links-container">
        <div class="api-link-title">🔑 获取 API 密钥</div>
        <a href="https://platform.deepseek.com/api_keys" target="_blank" class="api-link-button">
            <span class="api-link-icon">🚀</span> Deepseek API 密钥申请
        </a>
        <a href="https://cloud.siliconflow.cn/account/ak" target="_blank" class="api-link-button">
            <span class="api-link-icon">⚡</span> Siliconflow API 密钥申请
        </a>
        <a href="https://bailian.console.aliyun.com/?apiKey=1#/api-key" target="_blank" class="api-link-button">
            <span class="api-link-icon">🔮</span> 阿里云通义千问 API 密钥申请
        </a>
    </div>
    """, unsafe_allow_html=True)
    
    # 获取当前类型对应的存储的 API key
    current_api_key = (st.session_state.api_key_deepseek 
                      if api_key_type == "deepseek"
                      else st.session_state.api_key_qwen
                      if api_key_type == "qwen" 
                      else st.session_state.api_key_siliconflow)
    
    # 在侧边栏创建一个输入框让用户输入 API 密钥
    api_key = st.sidebar.text_input(
        f"输入您的 {api_key_type.upper()} API 密钥",
        value=current_api_key,
        type="password"
    )
    
    # 更新对应类型的 API key
    if api_key_type == "deepseek":
        st.session_state.api_key_deepseek = api_key
    elif api_key_type == "qwen":
        st.session_state.api_key_qwen = api_key
    else:
        st.session_state.api_key_siliconflow = api_key
    
    # 添加一个提示，说明 API 密钥的重要性
    if not api_key:
        st.sidebar.warning("请输入有效的 API 密钥以使用所有功能。")
    
    return api_key, api_key_type




def get_user_files_with_doc_ids(username, limit=200):
    user_files = []
    doc_ids = {}
    files_with_doc_ids = {}

    # 读取 user_files.json
    if os.path.exists(USER_FILES_JSON):
        try:
            with open(USER_FILES_JSON, 'r', encoding='utf-8') as f:
                user_data = json.load(f)
                user_files = user_data.get(username, [])
        except json.JSONDecodeError:
            print(f"警告: {USER_FILES_JSON} 不是有效的 JSON 格式")
        except Exception as e:
            print(f"读取 {USER_FILES_JSON} 时发生错误: {str(e)}")

    
    if os.path.exists(DOC_IDS_JSON):
        # 使用检测到的编码读取文件
        with open(DOC_IDS_JSON, 'r', encoding='utf-8') as f:
             doc_ids = json.load(f)
    # 筛选出有 doc_id 的文件，并创建文件名到 doc_id 的映射
    for file in reversed(user_files):  # 从最新的文件开始
        if file in doc_ids:
            files_with_doc_ids[file] = doc_ids[file]
            if len(files_with_doc_ids) == limit:  # 只保留最新的 limit 个文件
                break
    return files_with_doc_ids








def is_allowed_file(filename):
    return os.path.splitext(filename)[1].lower() in ALLOWED_EXTENSIONS

def update_user_files(username, filename):
    os.makedirs(os.path.dirname(USER_FILES_JSON), exist_ok=True)
    user_files = {}
    
    if os.path.exists(USER_FILES_JSON):
        try:
            with open(USER_FILES_JSON, 'r', encoding='utf-8') as f:
                content = f.read()
                if content:
                    user_files = json.loads(content)
                else:
                    print(f"警告: {USER_FILES_JSON} 是空文件")
        except json.JSONDecodeError:
            print(f"警告: {USER_FILES_JSON} 不是有效的 JSON 格式，将创建新的文件")
        except Exception as e:
            print(f"读取 {USER_FILES_JSON} 时发生错误: {str(e)}")
    
    if username not in user_files:
        user_files[username] = []

    if filename not in user_files[username]:
        user_files[username].append(filename)

    try:
        with open(USER_FILES_JSON, 'w', encoding='utf-8') as f:
            json.dump(user_files, f, indent=2)
    except Exception as e:
        print(f"写入 {USER_FILES_JSON} 时发生错误: {str(e)}")



        
# 添加自定义样式
st.markdown("""
    <style>
    /* 容器样式 */
    .checkbox-container {
        background: linear-gradient(135deg, #FFF8DC, #FFEFD5);
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 2px 12px rgba(230, 184, 77, 0.15);
        border: 1px solid rgba(230, 184, 77, 0.2);
        margin: 15px 0;
    }
    
    /* 文件选择行样式 */
    .file-row {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;  /* 减少间距 */
        margin-bottom: 8px !important;
    }
    
    /* 复选框容器样式 */
    .stCheckbox {
        flex: 1 !important;
        height: 40px !important;
        padding: 0 12px !important;
        background: linear-gradient(135deg, #FFF8DC, #FFEFD5) !important;
        border-radius: 8px !important;
        border: 1px solid rgba(230, 184, 77, 0.3) !important;
        transition: all 0.3s ease !important;
        display: flex !important;
        align-items: center !important;
        overflow: hidden !important;
        white-space: nowrap !important;
        margin-left: 8px !important;  /* 移除左边距 */            
    }
    
    /* 复选框内部容器 */
    .stCheckbox > div {
        display: flex !important;
        align-items: center !important;
        height: 100% !important;
        width: 100% !important;
    }
    
    /* 复选框文本 */
    .stCheckbox label {
        color: #8B7355 !important;
        font-size: 0.9em !important;
        padding: 0 8px !important;
        flex: 1 !important;
    }
    
    /* 复选框文本内容 */
    .stCheckbox label p {
        margin: 0 !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        white-space: nowrap !important;
        max-width: 200px !important;
    }
    

    
    /* 交互状态 */
    .stCheckbox:hover {
        background: linear-gradient(135deg, #FFEFD5, #FFE4B5) !important;
        box-shadow: 0 2px 8px rgba(230, 184, 77, 0.2) !important;
        transform: translateY(-2px);
    }
    
    .stCheckbox:has(input:checked) {
        background: linear-gradient(135deg, #FFE4B5, #FFD700) !important;
        border-color: rgba(230, 184, 77, 0.6) !important;
    }
    
    /* 列布局调整 */
    [data-testid="column"] {
        padding: 0 !important;
        display: flex !important;
        align-items: center !important;
    }
    </style>
""", unsafe_allow_html=True)

# 添加删除文件的函数
def delete_user_file(username, filename):
    if os.path.exists(USER_FILES_JSON):
        try:
            with open(USER_FILES_JSON, 'r', encoding='utf-8') as f:
                user_files = json.load(f)
                
            if username in user_files and filename in user_files[username]:
                user_files[username].remove(filename)
                
                with open(USER_FILES_JSON, 'w', encoding='utf-8') as f:
                    json.dump(user_files, f, indent=2)
                return True
        except Exception as e:
            print(f"删除文件记录时发生错误: {str(e)}")
    return False


def file_upload_component(username):
    # 初始化 session_state
    if 'file_uploaded' not in st.session_state:
        st.session_state.file_uploaded = False
    # 使用列来控制上传组件的宽度
    current_uploads = manage_user_rag_count(username)
    limits = get_user_upload_limits(username)
    remaining_uploads = limits['rag'] - current_uploads
    if remaining_uploads > 0:
        uploaded_file = st.file_uploader("上传文件", type=[ext[1:] for ext in ALLOWED_EXTENSIONS],
        label_visibility="collapsed") # 隐藏标签但保持可访问性  
    else:
        st.warning("已达到最大上传次数限制。")
        uploaded_file = None
    
    if uploaded_file is not None and not st.session_state.file_uploaded:
        if is_allowed_file(uploaded_file.name):
            # 修改文件名为 username+原名
            original_filename = uploaded_file.name
            new_filename = f"{username}_{original_filename}"
            file_path = os.path.join(RAGDOC_DIR, new_filename)
            
            with open(file_path, "wb") as f:
                f.write(uploaded_file.getbuffer())
            
            # 更新用户文件记录时也使用新的文件名
            update_user_files(username, new_filename)
            st.success(f"文件 '{original_filename}' 已成功上传到 ragdoc 目录")
            
            # 增加用户的上传次数
            current_uploads = manage_user_rag_count(username, increment=True)
            remaining_uploads = max_rag_uploads - current_uploads
            st.markdown(f'<div class="uploadInfo">剩余上传次数: {remaining_uploads}</div>', unsafe_allow_html=True)
            logging.info(f"After upload: User {username} current uploads: {current_uploads}, remaining: {remaining_uploads}")
            
            # 设置上传标志
            st.session_state.file_uploaded = True
            
            # 清除文件上传器的状态，以便下次上传
            st.session_state['uploaded_file'] = None
            st.rerun()
        else:
            st.error("不支持的文件类型。请上传允许的文件类型。")

    # 重置上传标志
    if st.session_state.file_uploaded:
        st.session_state.file_uploaded = False
    return remaining_uploads









def video_chat_interface(video_name, conversation_id,username,password,token, dataset_id):
    if 'form_counter' not in st.session_state:
        st.session_state.form_counter = 0
    VIDEO_BASE_PATH = os.path.join(SCRIPT_DIR, "video")
    subtitle_name = os.path.splitext(video_name)[0] + ".json"
    subtitle_path = os.path.join(VIDEO_BASE_PATH, subtitle_name)
    # Initialize doc_id to None
    doc_id = None
    # 检查是否需要重新上传字幕文件
    if os.path.exists(subtitle_path):    
        doc_id = upload_or_get_doc_id(subtitle_path, "test", api_key_model, parser_id="naive")
        # st.info("正在添加字幕到知识库中")
        if doc_id:
            st.success(f"视频文件解析成功,文档ID: {doc_id}")
            # st.markdown(f"视频文件解析成功,doc_id: {doc_id}") 
            st.session_state.doc_id = doc_id
            st.session_state.current_video = video_name
            # 不再重置聊天历史
            if 'chat_history' not in st.session_state:
                st.session_state.chat_history = []
                
        else:
            st.error("字幕文件正在上传，预计1-2分钟请稍等...")
    else:
        st.error(f"字幕文件不存在: {subtitle_path}")

    if 'doc_id' in st.session_state and doc_id:
        HTML_TEMPLATE1 = r"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>AI 视频对话</title>
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/katex.min.css">
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.5.1/styles/default.min.css">
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    max-width: 100%;
                    height: 100%;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #FFFBE6;  /* 基础淡黄色背景 */
                }}
                #aiChatSection {{
                    position: absolute;  /* 固定定位 */
                    top: 0;  /* 距离顶部0 */
                    left: 0;  /* 距离左侧0 */
                    min-width: 500px;
                    max-width: 100%;  /* 设置宽度为100% */
                    height: 600px;
                    max-height: 100%;  /* 设置高度为100% */
                    background-color: #FFF8DC;  /* 稍深的淡黄色 */
                    border-radius: 12px;
                    box-shadow: 0 2px 12px rgba(160, 140, 90, 0.1);  /* 柔和的阴影 */
                    padding: 25px;
                    border: 1px solid #F3E2A9;  /* 温暖的边框色 */
                    resize: both;  /* 允许从右下角拉伸 */
                    overflow: auto;  /* 允许滚动 */
                    cursor: move;  /* 鼠标悬停时显示为移动光标 */
                }}
                /* 拖动手柄样式 */
                .drag-handle {{
                    width: 100%;
                    height: 20px;  /* 拖动手柄的高度 */
                    background-color: #E6B84D;  /* 拖动手柄的颜色 */
                    cursor: move;  /* 鼠标悬停时显示为移动光标 */
                    border-radius: 12px 12px 0 0;  /* 圆角 */
                }}
                #chatHistory {{
                    height: calc(100% - 300px);  /* 计算高度，留出输入框和标题的空间 */
                    width: calc(100% - 40px);
                    resize: vertical;
                    overflow-y: auto;
                    border: 1px solid #F3E2A9;
                    border-radius: 8px;
                    padding: 15px;
                    margin-bottom: 20px;
                    background-color: #FFFDF0;  /* 更柔和的背景色 */
                }}
                .chat-input-container {{
                    display: flex;
                    gap: 12px;
                }}
                #userInput {{
                    flex-grow: 1;
                    padding: 12px;
                    border: 1px solid #F3E2A9;
                    border-radius: 8px;
                    font-size: 16px;
                    height: 30px;
                    resize: vertical;
                    overflow-y: auto;
                    min-height: 30px;
                    max-height: 300px;
                    background-color: #FFFDF0;
                    transition: border-color 0.3s ease;
                }}
                #userInput:focus {{
                    outline: none;
                    border-color: #E6B84D;
                    box-shadow: 0 0 5px rgba(230, 184, 77, 0.2);
                }}
                button {{
                    padding: 10px 24px;
                    background-color: #F0C674;  /* 温暖的按钮颜色 */
                    color: #5A4A1C;  /* 深褐色文字 */
                    border: none;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 16px;
                    font-weight: 500;
                    transition: all 0.3s ease;
                }}
                button:hover {{
                    background-color: #E6B84D;
                    transform: translateY(-1px);
                }}
                button:disabled {{
                    background-color: #F5E6B3;
                    cursor: not-allowed;
                    transform: none;
                }}
                .you-message, .ai-message, .system-message {{
                    padding: 12px 16px;
                    margin-bottom: 12px;
                    border-radius: 8px;
                    max-width: 85%;
                    word-wrap: break-word;
                    box-shadow: 0 1px 3px rgba(160, 140, 90, 0.1);
                }}
                .you-message {{
                    background-color: #F5F0D0;  /* 用户消息背景 */
                    margin-left: auto;
                    border: 1px solid #F3E2A9;
                }}
                .ai-message {{
                    background-color: #FFF8E7;  /* AI消息背景 */
                    margin-right: auto;
                    border: 1px solid #F3E2A9;
                }}
                .system-message {{
                    background-color: #FFF3E0;  /* 系统消息背景 */
                    margin: 10px auto;
                    text-align: center;
                    font-style: italic;
                    border: 1px solid #FFE4B5;
                }}
                pre code {{
                    display: block;
                    background-color: #FFFDF0;
                    padding: 1em;
                    border-radius: 8px;
                    overflow-x: auto;
                    border: 1px solid #F3E2A9;
                }}
                /* 滚动条样式 */
                #chatHistory::-webkit-scrollbar {{
                    width: 8px;
                }}
                #chatHistory::-webkit-scrollbar-track {{
                    background: #FFF8DC;
                    border-radius: 4px;
                }}
                #chatHistory::-webkit-scrollbar-thumb {{
                    background: #F0C674;
                    border-radius: 4px;
                }}
                #chatHistory::-webkit-scrollbar-thumb:hover {{
                    background: #E6B84D;
                }}
                /* 标题样式 */
                h2 {{
                    color: #5A4A1C;
                    text-align: center;
                    margin-bottom: 20px;
                    font-weight: 500;
                    font-size: 24px;
                }}
                #remainingChats {{
                    color: #8B7355;
                    text-align: center;
                    font-size: 14px;
                    margin-bottom: 15px;
                    font-weight: 500;
                }}
                /* 强调文本样式 */
                strong {{
                    color: #5A4A1C;
                    font-weight: 600;
                }}
            </style>
            <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
            <script src="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/katex.min.js"></script>
            <script src="https://cdn.jsdelivr.net/npm/katex@0.13.11/dist/contrib/auto-render.min.js"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.5.1/highlight.min.js"></script>
        </head>
        <body>
                <div id="aiChatSection" onmousedown="startDrag(event)">
                <div class="drag-handle" onmousedown="startDrag(event)"></div>  <!-- 拖动手柄 -->
                <h2>AI 视频对话助手</h2>
                <p id="remainingChats">每日对话次数上限: 50次 <span id="chatCountDisplay" style="display: none;">| 剩余对话次数: <span id="chatCount"></span></span></p>
                <div id="chatHistory"></div>
                <div class="chat-input-container">
                    <textarea id="userInput" placeholder="输入你的问题..."></textarea>
                    <button id="sendButton" onclick="sendMessage()">发送</button>
                </div>
            </div>
            <script>
                let isDragging = false;
                let offsetX, offsetY;           
                const urlParams = new URLSearchParams(window.location.search);
                const user = "{user}";
                const key = "{key}";
                const doc_id = "{doc_ids}";
                const token = "{token}";
                const dataset_id = "{dataset_id}";
                const conversation_id = "{conversation_id}";
                // 添加消息历史数组
                let messageHistory = [];
                const MAX_WORDS = 20000;  // 中英文字符总数限制

                // 计算文本中的汉字和英文单词数
                function countWords(text) {{
                    // 匹配汉字
                    const chineseChars = text.match(/[\u4e00-\u9fa5]/g) || [];
                    // 匹配英文单词（包括数字和连字符）
                    const englishWords = text.match(/[a-zA-Z]+(-[a-zA-Z]+)*|\d+/g) || [];
                    
                    return {{
                        total: chineseChars.length + englishWords.length,
                        chinese: chineseChars.length,
                        english: englishWords.length
                    }};
                }}

                function startDrag(e) {{
                    const chatSection = document.getElementById('aiChatSection');
                    const rect = chatSection.getBoundingClientRect();
                    const edgeMargin = 35; // 距离边缘的范围
                    const cornerMargin = 20; // 右下角排除的范围

                    // 检查鼠标是否在边缘范围内
                    const isNearEdge = 
                        (e.clientX < rect.left + edgeMargin || 
                        e.clientX > rect.right - edgeMargin || 
                        e.clientY < rect.top + edgeMargin || 
                        e.clientY > rect.bottom - edgeMargin) &&
                        !(e.clientX > rect.right - cornerMargin && e.clientY > rect.bottom - cornerMargin); // 排除右下角


                    if (!isNearEdge) {{
                        return; // 如果不在边缘范围内，不进行拖动
                    }}  

                    isDragging = true;
                    offsetX = e.clientX - rect.left;
                    offsetY = e.clientY - rect.top;
                    document.addEventListener('mousemove', drag);
                    document.addEventListener('mouseup', stopDrag);
                }}  

                function drag(e) {{
                    if (isDragging) {{
                        const chatSection = document.getElementById('aiChatSection');
                        chatSection.style.left = e.clientX - offsetX + 'px';
                        chatSection.style.top = e.clientY - offsetY + 'px';
                    }}
                }}

                function stopDrag() {{
                    isDragging = false;
                    document.removeEventListener('mousemove', drag);
                    document.removeEventListener('mouseup', stopDrag);
                }}


                // 管理消息历史的函数
                function manageMessageHistory(history) {{
                    let totalWords = 0;
                    let managedHistory = [];
                    
                    // 从最新的消息开始遍历
                    for (let i = history.length - 1; i >= 0; i--) {{
                        const message = history[i];
                        const messageText = `${{message.sender}}: ${{message.content}}\n`;
                        const wordCount = countWords(messageText);
                        
                        // 如果添加当前消息后总字数仍在限制内
                        if (totalWords + wordCount.total <= MAX_WORDS) {{
                            managedHistory.unshift(message);  // 在开头添加消息
                            totalWords += wordCount.total;
                            console.log(`Message added. Chinese: ${{wordCount.chinese}}, English: ${{wordCount.english}}, Total: ${{totalWords}}`);
                        }} else {{
                            // 如果是第一条消息且历史为空，确保至少保留一条
                            if (managedHistory.length === 0) {{
                                managedHistory.unshift(message);
                                console.log('Keeping at least one message');
                            }}
                            break;
                        }}
                    }}
                    
                    return managedHistory;
                }}

                // 修改 sendMessage 函数
                async function sendMessage() {{
                    const userInput = document.getElementById('userInput').value;
                    const sendButton = document.getElementById('sendButton');
                    if (!userInput) return;

                    // 添加用户消息到历史
                    messageHistory.push({{
                        sender: 'You',
                        content: userInput,
                        timestamp: new Date().toISOString()
                    }});
                    
                    appendMessage('You', userInput);
                    document.getElementById('userInput').value = '';
                    sendButton.disabled = true;
                    sendButton.textContent = '等待中...';

                    try {{
                        // 在发送之前管理历史记录大小
                        messageHistory = manageMessageHistory(messageHistory);
                        
                        const response = await fetch('/api/chat', {{
                            method: 'POST',
                            headers: {{
                                'Content-Type': 'application/json',
                            }},
                            body: JSON.stringify({{
                                message: userInput,
                                user: user,
                                key: key,
                                doc_id: doc_id,
                                conversationId: conversation_id,
                                dataset_id: dataset_id,
                                token: token,
                                history: messageHistory
                            }})
                        }});

                        const data = await response.json();


                        if (data.success) {{
                            // 添加 AI 响应到历史
                            messageHistory.push({{
                                sender: 'AI',
                                content: data.response,
                                timestamp: new Date().toISOString()
                            }});
                            
                            // 再次管理历史记录大小
                            messageHistory = manageMessageHistory(messageHistory);
                            
                            appendMessage('AI', data.response);
                            updateRemainingChats(data.remainingChats);
                            
                            // 输出当前历史记录状态
                            const totalCount = messageHistory.reduce((acc, msg) => {{
                                const count = countWords(msg.content);
                                return acc + count.total;
                            }}, 0);
                            console.log(`Current history word count: ${{totalCount}}/${{MAX_WORDS}}`);
                        }} else {{
                            appendMessage('System', 'Error: ' + data.message);
                        }}
                    }} catch (error) {{
                        console.error('Error:', error);
                        appendMessage('System', 'An error occurred while sending the message.');
                    }} finally {{
                        sendButton.disabled = false;
                        sendButton.textContent = '发送';
                    }}
                }}

                


            // 确保这个函数被定义
            function updateRemainingChats(remainingChats) {{
                const chatCountDisplay = document.getElementById('chatCountDisplay');
                const chatCount = document.getElementById('chatCount');
                if (chatCountDisplay && chatCount) {{
                    chatCountDisplay.style.display = 'inline';
                    chatCount.textContent = remainingChats;
                }} else {{
                    console.warn('Chat count elements not found');
                }}
            }}

                function appendMessage(sender, message) {{
                    const chatHistory = document.getElementById('chatHistory');
                    const messageElement = document.createElement('div');
                    messageElement.className = sender.toLowerCase() + '-message';

                    // 使用 marked 解析 Markdown
                    const renderedContent = marked.parse(message);

                    messageElement.innerHTML = `<strong>${{sender}}:</strong> ${{renderedContent}}`;
                    chatHistory.appendChild(messageElement);
                    chatHistory.scrollTop = chatHistory.scrollHeight;

                    // 初始化新添加的元素
                    initializeMessageElement(messageElement);
                }}



                function initializeMessageElement(element) {{
                    // 渲染数学公式
            
                    renderMathInElement(element, {{
                        delimiters: [
                            {{left: "$$", right: "$$", display: true}},
                            {{left: "$", right: "$", display: false}}
                        ],
                        throwOnError: false
                    }});

                    // 应用代码高亮
                    element.querySelectorAll('pre code').forEach((block) => {{
                        hljs.highlightBlock(block);
                    }});
                }}
            </script>
        </body>
        </html>
        """

        # 使用 format 方法插入 user 和 key
        html_content = HTML_TEMPLATE1.format(user=username, key=password, doc_ids=doc_id, dataset_id=dataset_id, conversation_id=conversation_id, token=token)

        # 使用 Streamlit 的 components.html 来嵌入 HTML
        components.html(html_content, height=800,width=1000)


########################################################################


def load_video(video_name,username,password,token):
    """
    加载并显示视频播放器，包括字幕和下载选项
    
    Args:
        video_name (str): 视频文件名
        
    Returns:
        None: 使用streamlit直接渲染UI组件
    """
    try:
        # 初始化路径
        VIDEO_BASE_PATH = os.path.join(SCRIPT_DIR, "video")
        video_path = os.path.join(VIDEO_BASE_PATH, video_name)
        srt_subtitle_path = os.path.join(VIDEO_BASE_PATH, f"{os.path.splitext(video_name)[0]}.srt")

        # 验证视频文件
        if not os.path.exists(video_path):
            st.error(f"视频文件未找到: {video_path}")
            return

        # 检查文件大小
        try:
            file_size = os.path.getsize(video_path) / (1024 * 1024)  # Convert to MB
            if file_size > 500:  # 如果文件大于500MB
                st.warning(f"警告: 文件较大 ({file_size:.2f} MB)，加载可能需要较长时间")
            else:
                st.success(f"文件已找到. 大小: {file_size:.2f} MB")
        except OSError as e:
            st.error(f"无法获取文件大小: {str(e)}")
            return

        # 设置视频服务器URL
        VIDEO_SERVER_URL = get_origin() or "http://localhost:5015"
        video_url = f"{VIDEO_SERVER_URL}/video/{video_name}?user={username}&key={password}&token={token}"
        subtitle_url = f"{VIDEO_SERVER_URL}/subtitles/{video_name}"


        # 获取字幕
        subtitles = []
        
        try:
            response = requests.get(
                subtitle_url, 
                verify=False, 
                timeout=10,
                headers={'User-Agent': 'Mozilla/5.0'}
            )
            if response.status_code == 200:
                subtitles = response.json()
            else:
                st.warning(f"无法加载字幕 (状态码: {response.status_code}). 使用空字幕.")
        except requests.RequestException as e:
            st.warning(f"加载字幕时出错: {str(e)}. 使用空字幕.")
            logging.error(f"Subtitle loading error for {video_name}: {str(e)}")

        # 获取视频分辨率
        frame_width, frame_height = 800, 600  # 默认分辨率
        try:
            video_capture = cv2.VideoCapture(video_path)
            if video_capture.isOpened():
                frame_width = int(video_capture.get(cv2.CAP_PROP_FRAME_WIDTH))
                frame_height = int(video_capture.get(cv2.CAP_PROP_FRAME_HEIGHT))
                video_capture.release()
            else:
                st.warning("无法读取视频分辨率，使用默认值")
        except Exception as e:
            st.warning(f"读取视频分辨率时出错: {str(e)}")
            logging.error(f"Video resolution error for {video_name}: {str(e)}")
        finally:
            if 'video_capture' in locals():
                video_capture.release()

        # 创建视频播放器
        try:
            video_player_html = create_video_player_html(video_url, subtitles)
            
            # 计算播放器高度
            # player_height = calculate_player_height(frame_width, frame_height)

            if frame_width > frame_height:
                frame_height=int(frame_height * (900 / frame_width))
                frame_width=900
            else:
                frame_width=int(frame_width * (800 / frame_height))
                frame_height=800


 

            # 创建下载按钮容器
            download_col1, download_col2 = st.columns(2)

            print(f'frame_width:{frame_width},frame_height:{frame_height}')
            # 显示视频播放器
            st.components.v1.html(
                video_player_html, 
                height=frame_height,
                width=frame_width,
                scrolling=True
            )


            
            # 添加视频下载按钮
            with download_col1:
                st.markdown(
                    f'<a href="{video_url}" download="{video_name}" '
                    f'style="text-decoration:none;color:white;background-color:#0066cc;padding:8px 15px;'
                    f'border-radius:5px;display:inline-block;">'
                    f'📥 下载视频文件</a>', 
                    unsafe_allow_html=True
                )

            # 添加字幕下载按钮
            with download_col2:
                if os.path.exists(srt_subtitle_path):
                    try:
                        with open(srt_subtitle_path, "rb") as srt_file:
                            srt_contents = srt_file.read()
                            b64 = base64.b64encode(srt_contents).decode()
                            st.markdown(
                                f'<a href="data:file/srt;base64,{b64}" '
                                f'download="{os.path.basename(srt_subtitle_path)}" '
                                f'style="text-decoration:none;color:white;background-color:#28a745;padding:8px 15px;'
                                f'border-radius:5px;display:inline-block;">'
                                f'📝 下载字幕文件 (SRT)</a>',
                                unsafe_allow_html=True
                            )
                    except Exception as e:
                        st.error(f"读取字幕文件时出错: {str(e)}")
                        logging.error(f"SRT file reading error for {video_name}: {str(e)}")
                else:
                    st.warning("SRT 字幕文件还未生成，请稍后刷新获取")
        except Exception as e:
            st.error(f"创建视频播放器时发生错误: {str(e)}")
    except Exception as e:
        st.error(f"加载视频时发生错误: {str(e)}")
        logging.error(f"Video loading error for {video_name}: {str(e)}")





def create_video_player_html(video_url, subtitles):
    subtitles_json = json.dumps(subtitles)
    
    return f"""
    <html>
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="Permissions-Policy" content="ambient-light-sensor=(), battery=(), document-domain=(), layout-animations=(), legacy-image-formats=(), oversized-images=(), vr=(), wake-lock=()">
        <link rel="stylesheet" href="https://cdn.plyr.io/3.6.8/plyr.css" />
        <script src="https://cdn.plyr.io/3.6.8/plyr.polyfilled.js"></script>
        <style>
            body, html {{
                margin: 0;
                padding: 0;
                height: 100%;
                overflow: hidden;
            }}
            .video-wrapper {{
                position: relative;
                max-width: 1080px;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                background-color: #000;
            }}
            .video-container {{
                position: relative;
                max-width: 1080px;
                width: 100%;
                height: 100%;
                display: flex;
            }}
            .plyr {{
                width: 100%;
                height: 100%;
            }}
            .plyr video {{
                object-fit: contain;
                object-position: center;
                width: 100%;
                height: 100%;
            }}
            .subtitle-container {{
                position: absolute;
                bottom: 60px;
                left: 0;
                right: 0;
                background-color: rgba(0, 0, 0, 0.3);
                color: white;
                padding: 10px;
                text-align: center;
                font-size: 15px;
                z-index: 10;
                pointer-events: none;
                white-space: pre-wrap;
                display: flex;
                flex-direction: column;
                align-items: center;
            }}
            .plyr--fullscreen .subtitle-container {{
                bottom: 90px;
            }}
            /* 根据视频窗口大小调整字幕字体大小 */
            @media (max-width: 1080px) {{
                .subtitle-english, .subtitle-chinese {{
                    font-size: calc(18px + (24 - 12) * ((100vw - 320px) / (1080 - 320)));
                }}
            }}
            @media (max-width: 768px) {{
                .subtitle-english, .subtitle-chinese {{
                    font-size: calc(15px + (18 - 10) * ((100vw - 320px) / (768 - 320)));
                }}
            }}
            @media (max-width: 480px) {{
                .subtitle-english, .subtitle-chinese {{
                    font-size: 14px;
                }}
            }}
            #subtitleControls {{
                position: absolute;
                top: 10px;
                right: 10px;
                z-index: 11;
                background-color: rgba(0, 0, 0, 0.5);
                color: white;
                padding: 5px;
                border-radius: 5px;
                opacity: 0;
                transition: opacity 0.3s ease;
            }}
            #subtitleControls:hover {{
                opacity: 1;
            }}
            #subtitleControls button {{
                background-color: transparent;
                color: white;
                border: 1px solid white;
                margin: 0 2px;
                padding: 2px 5px;
                cursor: pointer;
            }}
            #subtitleControls button.active {{
                background-color: white;
                color: black;
            }}
            .video-container:hover #subtitleControls {{
                opacity: 1;
            }}
        </style>
    </head>
    <body>
        <div class="video-wrapper" id="videoWrapper">
            <div class="video-container" id="videoContainer">
                <video id="player" playsinline>
                    <source src="{video_url}" type="video/mp4">
                </video>
                <div id="subtitle" class="subtitle-container"></div>
                <div id="subtitleControls">
                    <button id="subtitleOff">Off</button>
                    <button id="subtitleEnglish">English</button>
                    <button id="subtitleChinese">中文</button>
                    <button id="subtitleBoth" class="active">Both</button>
                </div>
            </div>
        </div>
        
        <script>
            const player = new Plyr('#player', {{
                controls: ['play-large', 'play', 'progress', 'current-time', 'mute', 'volume', 'captions', 'settings', 'pip', 'airplay', 'fullscreen'],
                settings: ['captions', 'quality', 'speed', 'loop'],
            }});
            const videoWrapper = document.getElementById('videoWrapper');
            const videoContainer = document.getElementById('videoContainer');
            const subtitleDiv = document.getElementById('subtitle');
            const subtitleControls = document.getElementById('subtitleControls');
            const subtitles = {subtitles_json};
            let subtitleMode = 'both';
            
            function updateSubtitleSize() {{
                const videoHeight = videoContainer.clientHeight;
                const fontSize = Math.max(16, Math.round(videoHeight * 0.05));
                subtitleDiv.style.fontSize = `${{fontSize}}px`;
            }}
            
            function adjustVideoSize() {{
                updateSubtitleSize();
            }}
            
            player.on('ready', function() {{
                adjustVideoSize();
                // 将字幕容器和控制按钮移动到 Plyr 的容器中
                const plyrContainer = document.querySelector('.plyr');
                plyrContainer.appendChild(subtitleDiv);
                plyrContainer.appendChild(subtitleControls);
            }});
            
            player.on('enterfullscreen', adjustVideoSize);
            player.on('exitfullscreen', adjustVideoSize);
            
            window.addEventListener('resize', adjustVideoSize);
            
            function updateSubtitle(text) {{
                if (subtitleMode === 'off') {{
                    subtitleDiv.innerHTML = '';
                    return;
                }}
                
                // 找到最后一个换行符的位置
                const lastNewlineIndex = text.lastIndexOf('\\n');
                
                let englishText = '';
                let chineseText = '';

                // 检测是否包含汉字的函数
                function containsChinese(text) {{
                    return /[\u4e00-\u9fa5]/.test(text);
                }}

                if (lastNewlineIndex !== -1) {{
                    // 如果找到换行符，分割英文和中文
                    englishText = text.substring(0, lastNewlineIndex).replace(/\\n/g, ' ').trim();
                    chineseText = text.substring(lastNewlineIndex + 1).trim();
                }} else {{
                    // 如果没有找到换行符，检查是否包含汉字
                    if (containsChinese(text)) {{
                        chineseText = text.trim();
                    }} else {{
                        englishText = text.trim();
                    }}
                }}

                let subtitleHTML = '';
                if (subtitleMode === 'english' || subtitleMode === 'both') {{
                    subtitleHTML += englishText ? `<div class="subtitle-english">${{englishText}}</div>` : '';
                }}
                if (subtitleMode === 'chinese' || subtitleMode === 'both') {{
                    subtitleHTML += chineseText ? `<div class="subtitle-chinese">${{chineseText}}</div>` : '';
                }}
                
                subtitleDiv.innerHTML = subtitleHTML;
            }}
            
            player.on('timeupdate', function() {{
                const currentTime = player.currentTime;
                let currentSubtitle = "";
                
                let left = 0;
                let right = subtitles.length - 1;
                
                while (left <= right) {{
                    const mid = Math.floor((left + right) / 2);
                    const [start, end, text] = subtitles[mid];
                    
                    if (currentTime >= start && currentTime <= end) {{
                        currentSubtitle = text;
                        break;
                    }} else if (currentTime < start) {{
                        right = mid - 1;
                    }} else {{
                        left = mid + 1;
                    }}
                }}
                
                if (currentSubtitle) {{
                    updateSubtitle(currentSubtitle);
                }} else {{
                    subtitleDiv.innerHTML = '';
                }}
            }});

            function setSubtitleMode(mode) {{
                subtitleMode = mode;
                document.querySelectorAll('#subtitleControls button').forEach(btn => btn.classList.remove('active'));
                document.getElementById('subtitle' + mode.charAt(0).toUpperCase() + mode.slice(1)).classList.add('active');
            }}

            document.getElementById('subtitleOff').addEventListener('click', () => setSubtitleMode('off'));
            document.getElementById('subtitleEnglish').addEventListener('click', () => setSubtitleMode('english'));
            document.getElementById('subtitleChinese').addEventListener('click', () => setSubtitleMode('chinese'));
            document.getElementById('subtitleBoth').addEventListener('click', () => setSubtitleMode('both'));

            // 错误处理
            player.on('error', function(e) {{
                console.error('Error loading video:', e);
                console.error('Video URL:', '{video_url}');
                alert('Error loading video. Please check the console for more information.');
            }});

            // 加载成功日志
            player.on('loadeddata', function() {{
                console.log('Video loaded successfully');
                console.log('Video URL:', '{video_url}');
            }});

            // 网络状态监控
            player.on('stalled', function() {{
                console.warn('Video download has stalled');
            }});

            player.on('waiting', function() {{
                console.warn('Video is waiting for more data');
            }});

            // 打印视频 URL 到控制台
            console.log('Attempting to load video from:', '{video_url}');

            // 添加鼠标进入和离开事件
            let controlsTimeout;
            videoWrapper.addEventListener('mousemove', function() {{
                subtitleControls.style.opacity = '1';
                clearTimeout(controlsTimeout);
                controlsTimeout = setTimeout(() => {{
                    if (!subtitleControls.matches(':hover')) {{
                        subtitleControls.style.opacity = '0';
                    }}
                }}, 2000);
            }});

            subtitleControls.addEventListener('mouseleave', function() {{
                controlsTimeout = setTimeout(() => {{
                    subtitleControls.style.opacity = '0';
                }}, 2000);
            }});

            subtitleControls.addEventListener('mouseenter', function() {{
                clearTimeout(controlsTimeout);
            }});
        </script>
    </body>
    </html>
    """
















# ##########################################
#HTML格式定义
############################################

def get_image_as_base64(file_path):
    with open(file_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode()
    
def center_image(image_base64, image_type):
    return f"""
        <style>
        .center-image {{
            display: flex;
            justify-content: center;
            width: 100%;
        }}
        .center-image img {{
            max-width: 100%;
            height: auto;
        }}
        </style>
        <div class="center-image">
            <img src="data:image/{image_type};base64,{image_base64}" alt="Image">
        </div>
    """


########################################################

# 添加自定义 CSS 来控制 selectbox 的宽度和布局
st.markdown("""
    <style>
    div[data-baseweb="select"] > div {
        width: 120px;
    }
    .stSelectbox {
        display: inline-block;
    }
    .row-container {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
        gap: 1px;
    }
    .select-item {
        flex: 0 1 auto;
    }
    </style>
    """, unsafe_allow_html=True)
    # 添加自定义 CSS 样式





def upload_file_to_server(file, username):
    web_VIDEO_SERVER_URL = get_origin()
    local_VIDEO_SERVER_URL = "http://localhost:5015"
    VIDEO_SERVER_URL = web_VIDEO_SERVER_URL
    url = f"{VIDEO_SERVER_URL}/upload"
    
    # 重命名文件：username_原文件名
    original_filename = file.name
    new_filename = f"{username}_{original_filename}"
    
    # 使用新文件名创建文件对象
    files = {'file': (new_filename, file, 'video/mp4')}
    
    try:
        response = requests.post(url, files=files, timeout=300, verify=False)
        response_data = json.loads(response.text)
        if response.status_code == 200:
            return True, "上传成功", response_data.get('filename')
        elif response.status_code == 409:
            return False, "文件已存在", response_data.get('filename')
        else:
            return False, f"上传失败，服务器返回状态码: {response.status_code}, 错误信息: {response_data.get('error')}", None
    except requests.exceptions.RequestException as e:
        st.write(f"Exception occurred: {str(e)}")
        return False, f"上传过程中发生错误: {str(e)}", None

################################
#获取IP
def get_forwarded_ip():
    # 使用 st.context.headers 获取请求头
    headers = st.context.headers
    # Example: "X-Forwarded-For': '************, **************'"
    x_forwarded_for = headers.get('X-Forwarded-For', '')
    first_ip = x_forwarded_for.split(', ')[0] if x_forwarded_for else None

    return first_ip
# 读取访问计数
def load_visit_count():
    if os.path.exists(visit_count_file):
        try:
            with open(visit_count_file, 'r', encoding='utf-8') as f:
                return json.load(f).get('visit_count', 0)
        except (json.JSONDecodeError, ValueError):
            # 如果 JSON 解析失败，返回 0
            return 0
    return 0

# 保存访问计数
def save_visit_count(count):
    with open(visit_count_file, 'w', encoding='utf-8') as f:
        json.dump({'visit_count': count}, f)
##################################################
def get_default_config():
    return {
        'credentials': {
            'usernames': {}
        },
        'cookie': {
            'expiry_days': 30,
            'key': 'some_signature_key',
            'name': 'some_cookie_name'
        },
        'preauthorized': {
            'emails': []
        }
    }

def load_config():
    config_path = 'config.yaml'
    if not os.path.exists(config_path):
        return get_default_config()
    with open(config_path, 'r', encoding='utf-8') as file:
        return yaml.safe_load(file)

def save_config(config):
    with open('config.yaml', 'w', encoding='utf-8') as file:
        yaml.dump(config, file, default_flow_style=False, allow_unicode=True)

def verify_password(stored_password, provided_password):
    try:
        # 确保密码是字符串类型
        if isinstance(stored_password, int):
            stored_password = str(stored_password)
        if isinstance(provided_password, int):
            provided_password = str(provided_password)
        
        # 检查存储的密码是否是有效的bcrypt哈希
        if not str(stored_password).startswith('$2'):
            # 如果不是有效的bcrypt哈希，直接比较字符串
            return str(stored_password) == str(provided_password)
        
        return bcrypt.checkpw(provided_password.encode(), stored_password.encode())
    except Exception as e:
        # 出现异常时，记录错误并返回False
        print(f"Password verification error: {e}")
        # 如果出现错误，尝试直接比较字符串
        return str(stored_password) == str(provided_password)

def hash_password(password):
    # 确保密码是字符串类型
    if not isinstance(password, str):
        password = str(password)
    
    return bcrypt.hashpw(password.encode(), bcrypt.gensalt()).decode()

def login(username, password, config):
    try:
        # 确保用户名是字符串类型
        if not isinstance(username, str):
            username = str(username)
        
        if username in config['credentials']['usernames']:
            user_info = config['credentials']['usernames'][username]
            stored_password = user_info['password']      
            # 否则使用verify_password函数进行验证
            return verify_password(stored_password, password)
        return False
    except Exception as e:
        print(f"Login error: {e}")
        # 如果出现异常，尝试直接比较密码
        try:
            stored_password = config['credentials']['usernames'][username]['password']
            return str(stored_password) == str(password)
        except:
            return False

def register(username, password, email, config, user_class=None):
    try:
        # 确保用户名和密码是字符串类型
        if not isinstance(username, str):
            username = str(username)
        if not isinstance(password, str):
            password = str(password)
        
        if username in config['credentials']['usernames']:
            return False, "Username already exists"
        

        try:
            hashed_password = hash_password(password)
        except Exception as e:
            print(f"Password hashing error: {e}")
            # 如果哈希失败，直接存储原始密码
            hashed_password = password
            
        config['credentials']['usernames'][username] = {
            'password': hashed_password,
            'email': email
        }
        
        save_config(config)
        return True, "Registration successful"
    except Exception as e:
        print(f"Registration error: {e}")
        return False, f"Registration failed: {str(e)}"

def convert_plaintext_passwords_to_hash(config):
    """
    检查配置文件中的所有用户密码，将明文密码转换为哈希密码
    保留有class字段的用户的明文密码
    """
    modified = False
    for username, user_info in config['credentials']['usernames'].items():
        stored_password = user_info['password']
          
        # 如果密码不是以$2开头的bcrypt哈希，则进行哈希处理
        if not str(stored_password).startswith('$2'):
            print(f"Converting plaintext password for user: {username}")
            user_info['password'] = hash_password(stored_password)
            modified = True 
    # 如果有修改，保存配置文件
    if modified:
        save_config(config)
        print("Password conversion completed and config saved.")
    
    return config

#################################################################################




#################################################################################
# 记录用户上传的视频函数
#################################################################################
def record_user_video(username, video_name):
    user_videos_file = os.path.join(SCRIPT_DIR, "user_videos.json")
    user_videos = {}
    
    # 检查文件是否存在，如果不存在则创建
    if not os.path.exists(user_videos_file):
        try:
            with open(user_videos_file, 'w', encoding='utf-8') as f:
                json.dump({}, f)
            print(f"已创建 {user_videos_file}")
        except Exception as e:
            print(f"创建 {user_videos_file} 时发生错误: {str(e)}")
            return  # 如果无法创建文件，则退出函数
    
    # 尝试读取现有数据
    try:
        with open(user_videos_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if content:
                user_videos = json.loads(content)
            else:
                print(f"警告: {user_videos_file} 是空文件")
    except json.JSONDecodeError:
        print(f"警告: {user_videos_file} 不是有效的 JSON 格式，将使用空字典")
    except Exception as e:
        print(f"读取 {user_videos_file} 时发生错误: {str(e)}")
    
    # 更新用户视频列表
    if username not in user_videos:
        user_videos[username] = []
    
    if video_name not in user_videos[username]:
        user_videos[username].append(video_name)
    
    # 写入更新后的数据
    try:
        with open(user_videos_file, 'w') as f:
            json.dump(user_videos, f, indent=2)
        print(f"已成功更新 {user_videos_file}")
    except Exception as e:
        print(f"写入 {user_videos_file} 时发生错误: {str(e)}")

def get_user_mp4_files(username):
    user_videos_file = os.path.join(SCRIPT_DIR, "user_videos.json")
    if os.path.exists(user_videos_file):
        try:
            with open(user_videos_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if content:
                    user_videos = json.loads(content)
                    return user_videos.get(username, [])
                else:
                    print(f"警告: {user_videos_file} 是空文件")
                    return []
        except json.JSONDecodeError:
            print(f"警告: {user_videos_file} 不是有效的 JSON 格式，将返回空列表")
            return []
        except Exception as e:
            print(f"读取 {user_videos_file} 时发生错误: {str(e)}")
            return []
    else:
        print(f"警告: {user_videos_file} 不存在，将返回空列表")
        return []


#记录用户上传次数
def manage_user_upload_count(username, increment=False):
    upload_count_file = 'user_upload_counts.json'
    

    # 如果文件不存在，创建一个空的 JSON 文件
    if not os.path.exists(upload_count_file):
        with open(upload_count_file, 'w', encoding='utf-8') as f:
            json.dump({}, f)

    # 读取当前的上传次数
    with open(upload_count_file, 'r', encoding='utf-8') as f:
        upload_counts = json.load(f)

    # 如果用户不在记录中，初始化为0
    if username not in upload_counts or username=="admin":
        upload_counts[username] = 0

    # 如果需要增加计数
    if increment:
        upload_counts[username] += 1

    # 写回文件
    with open(upload_count_file, 'w', encoding='utf-8') as f:
        json.dump(upload_counts, f)

    # 返回剩余的上传次数
    limits = get_user_upload_limits(username)
    remaining_uploads = limits['video'] - upload_counts[username]
    return remaining_uploads
#############################################################################
#记录用户提问次数
#############################################################################
def manage_user_submit_count(username, method=0, increment=False):
    submit_count_file = 'user_submit_counts.json'
    base_max_submits = 5
    extra_submits = 50 if method == 1 else 0
    max_submits = base_max_submits + extra_submits

    # 如果文件不存在，创建一个空的 JSON 文件
    if not os.path.exists(submit_count_file):
        with open(submit_count_file, 'w') as f:
            json.dump({}, f)

    # 读取当前的提交次数
    with open(submit_count_file, 'r', encoding='utf-8') as f:
        submit_counts = json.load(f)

    # 如果用户不在记录中，初始化为0
    if username not in submit_counts or username=="admin":
        submit_counts[username] = {'count': 0, 'method': method}
    elif submit_counts[username]['method'] != method:
        # 如果用户切换了方法，重置计数
        submit_counts[username] = {'count': 0, 'method': method}

    # 如果需要增加计数
    if increment:
        submit_counts[username]['count'] += 1

    # 写回文件
    with open(submit_count_file, 'w', encoding='utf-8') as f:
        json.dump(submit_counts, f)

    # 返回剩余的提交次数
    return max_submits - submit_counts[username]['count']


#记录用户上传rag次数
def manage_user_rag_count(username, increment=False):
    try:
        # 如果文件不存在，创建一个空的 JSON 文件
        if not os.path.exists(USER_RAG_COUNTS_FILE):
            with open(USER_RAG_COUNTS_FILE, 'w', encoding='utf-8') as f:
                json.dump({}, f, ensure_ascii=False)
            logging.info(f"Created new file: {USER_RAG_COUNTS_FILE}")

        # 读取当前的上传次数
        with open(USER_RAG_COUNTS_FILE, 'r', encoding='utf-8') as f:
            upload_counts = json.load(f)

        # 如果用户不在记录中，初始化为0
        if username not in upload_counts or username=="admin":
            upload_counts[username] = 0
            logging.info(f"Initialized count for user: {username}")

        # 如果需要增加计数
        if increment:
            upload_counts[username] = min(upload_counts[username] + 1, max_rag_uploads)
            logging.info(f"Incremented count for user: {username}. New count: {upload_counts[username]}")

        # 写回文件
        with open(USER_RAG_COUNTS_FILE, 'w', encoding='utf-8') as f:
            json.dump(upload_counts, f, indent=2, ensure_ascii=False)
        logging.info(f"Updated {USER_RAG_COUNTS_FILE}")

        # 返回当前的上传次数
        current_count = upload_counts[username]
        logging.info(f"Current upload count for {username}: {current_count}")
        return current_count

    except Exception as e:
        logging.error(f"Error in manage_user_rag_count: {str(e)}")
        return 0  # 如果出错，返回0






#############################################################################

#############################################################################
#用户反馈函数
#############################################################################
def save_user_feedback(username, feedback):
    feedback_dir = os.path.join(SCRIPT_DIR, "user_feedback")
    if not os.path.exists(feedback_dir):
        os.makedirs(feedback_dir)
    
    feedback_file = os.path.join(feedback_dir, "all_user_feedback.json")
    
    if os.path.exists(feedback_file):
        with open(feedback_file, 'r', encoding='utf-8') as f:
            feedback_data = json.load(f)
    else:
        feedback_data = {}
    
    if username not in feedback_data:
        feedback_data[username] = []
    
    feedback_entry = {
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "feedback": feedback,
        "index": len(feedback_data[username]) + 1
    }
    
    feedback_data[username].append(feedback_entry)
    
    with open(feedback_file, 'w', encoding='utf-8') as f:
        json.dump(feedback_data, f, ensure_ascii=False, indent=4)

def submit_feedback():
    if st.session_state.user_feedback:
        save_user_feedback(st.session_state.username, st.session_state.user_feedback)
        st.session_state.user_feedback = ""
        st.session_state.feedback_submitted = True
    else:
        st.session_state.feedback_submitted = False
#############################################################################

def get_user_password(username, config_file='config.yaml'):
    with open(config_file, 'r', encoding='utf-8') as file:
        config = yaml.safe_load(file)
    return config['credentials']['usernames'].get(username, {}).get('password', '')



############################################################################
#美化blog入口
############################################################################
def create_styled_button(url, text, emoji, color_scheme):
    button_style = f"""
        <style>
        .styledButton_{color_scheme['name']} {{
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(45deg, {color_scheme['gradient'][0]}, {color_scheme['gradient'][1]});
            color: {color_scheme['text']};
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            text-decoration: none;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }}
        .styledButton_{color_scheme['name']}:hover {{
            background: linear-gradient(45deg, {color_scheme['gradient'][1]}, {color_scheme['gradient'][0]});
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }}
        .buttonEmoji {{
            margin-right: 8px;
            font-size: 20px;
        }}
        </style>
    """
    button_html = f'{button_style}<a href="{url}" target="_blank" class="styledButton_{color_scheme["name"]}"><span class="buttonEmoji">{emoji}</span>{text}</a>'
    return button_html





def get_origin():
    # 使用 st.context.headers 获取请求头
    headers = st.context.headers  
    # 获取 Origin 头部
    origin = headers.get('Origin', 'https://xpuai.20140806.xyz')  # 默认值作为后备
    return origin

# 添加自定义 CSS 样式
st.markdown("""
    <style>
    .custom-header {
        color: #5A4A1C;  /* 深色文字 */
        background-color: #FFF8DC;  /* 稍深的淡黄色背景 */
        padding: 10px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        font-weight: bold;
        text-align: center;
        margin-bottom: 20px;
        max-width: 600px;  /* 限制最大宽度 */
        margin-left: auto;
        margin-right: auto;
    }
    .custom-subheader {
        color: #8B7355;  /* 中等深度的文字颜色 */
        background-color: #FFFDF0;  /* 更柔和的背景色 */
        padding: 10px;
        border-radius: 6px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        font-weight: bold;  /* 加粗字体 */
        text-align: center;
        margin-bottom: 15px;
        max-width: 500px;  /* 限制最大宽度 */
        margin-left: auto;
        margin-right: auto;
        height: 40px; /* 设置固定高度 */
        line-height: 30px; /* 垂直居中对齐文本 */
    }
            

    .custom-note {
        color: #5A4A1C;  /* 深色文字 */
        background-color: #FFFBE6;  /* 最浅的淡黄色背景 */
        padding: 10px;
        border-left: 5px solid #F0C674;  /* 左侧边框颜色 */
        border-radius: 4px;
        font-style: italic;
        text-align: left;
        margin-bottom: 10px;
        max-width: 500px;  /* 限制最大宽度 */
        margin-left: 40px;
        margin-right: auto;
        position: relative;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    .custom-note:before {
        content: "💡";  /* 提示图标 */
        position: absolute;
        left: -40px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 28px;
        color: #F0C674;
    }
    </style>
""", unsafe_allow_html=True)

# 添加自定义 CSS 样式
st.markdown("""
    <style>
    .selectbox-container {
        background-color: #FFF8DC;  /* 稍深的淡黄色背景 */
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
        text-align: center;
    }
    .selectbox-title {
        color: #5A4A1C;  /* 深色文字 */
        font-weight: bold;
        margin-bottom: 10px;
    }
    </style>
""", unsafe_allow_html=True)



# 添加自定义 CSS 样式
st.markdown("""
    <style>
    .visit-count-container {
        background-color: #FFF8DC;  /* 稍深的淡黄色背景 */
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        text-align: center;
        margin-bottom: 20px;
        max-width: 300px;
        margin-left: auto;
        margin-right: auto;
    }
    .visit-count-title {
        color: #5A4A1C;  /* 深色文字 */
        font-weight: bold;
        font-size: 18px;
        margin-bottom: 5px;
    }
    .visit-count-number {
        color: #8B7355;  /* 中等深度的文字颜色 */
        font-size: 24px;
        font-weight: bold;
    }
    </style>
""", unsafe_allow_html=True)

st.markdown("""
    <style>
    .ip-address-container {
        background-color: #FFF8DC;  /* 稍深的淡黄色背景 */
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        text-align: center;
        margin-bottom: 20px;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }
    .ip-address-title {
        color: #5A4A1C;  /* 深色文字 */
        font-weight: bold;
        font-size: 18px;
        margin-bottom: 5px;
    }
    .ip-address-value {
        color: #8B7355;  /* 中等深度的文字颜色 */
        font-size: 16px;
        font-weight: bold;
    }
    </style>
""", unsafe_allow_html=True)


# 添加自定义 CSS 样式
st.markdown("""
    <style>
    /* 侧边栏背景和整体样式 */
    .css-1d391kg {  /* Streamlit 侧边栏的类名 */
        background-color: #F5E6B3;  /* 稍浅的米黄色，与主页的 #FFF8DC 形成对比 */
    }
    
    /* 侧边栏分割线 */
    .sidebar-divider {
        margin: 25px 0;
        height: 3px;
        border: none;
        background: linear-gradient(90deg, 
            #FFD700,
            #FFA500,
            #FF8C00,
            #E6B84D,
            #FFD700
        );
        background-size: 200% 100%;
        border-radius: 3px;
        box-shadow: 0 2px 4px rgba(230, 184, 77, 0.3);
        position: relative;
        animation: shimmer 3s infinite linear;
    }

    .sidebar-divider::before,
    .sidebar-divider::after {
        content: '✨';
        position: absolute;
        font-size: 14px;
        top: -10px;
        animation: sparkle 2s infinite ease-in-out;
    }

    .sidebar-divider::before {
        left: 5px;
    }

    .sidebar-divider::after {
        right: 5px;
    }

    @keyframes shimmer {
        0% {
            background-position: 200% 0;
        }
        100% {
            background-position: -200% 0;
        }
    }

    @keyframes sparkle {
        0%, 100% {
            opacity: 0.4;
            transform: scale(0.8);
        }
        50% {
            opacity: 1;
            transform: scale(1.2);
        }
    }

    /* 鼠标悬停效果 */
    .sidebar-divider:hover {
        height: 4px;
        animation-duration: 1.5s;
        box-shadow: 0 2px 8px rgba(230, 184, 77, 0.5);
    }

    .sidebar-divider:hover::before,
    .sidebar-divider:hover::after {
        animation-duration: 1s;
    }
    
    /* 侧边栏标题样式 */
    .sidebar-title {
        color: #5A4A1C;
        font-size: 18px;
        font-weight: bold;
        padding: 10px 0;
        margin-bottom: 10px;
    }
    
    /* 侧边栏链接样式 */
    .sidebar-link {
        color: #8B7355;
        text-decoration: none;
        padding: 5px 10px;
        border-radius: 5px;
        display: block;
        margin: 5px 0;
        transition: all 0.3s ease;
    }
    .sidebar-link:hover {
        background-color: #E6B84D;
        color: #5A4A1C;
    }
    
    /* 侧边栏文本区域样式 */
    .stTextArea > div > div > textarea {
        background-color: #FFFDF0;
        border: 1px solid #E6B84D;
        border-radius: 5px;
        color: #5A4A1C;
    }
    

    .stSelectbox > div > div > div {
        background-color: #FFF8DC;  /* 稍深的淡黄色背景，与页面一致 */
        color: #5A4A1C;  /* 深色文字 */
        border: 1px solid #E6B84D;  /* 边框颜色 */
        border-radius: 8px;  /* 圆角 */
        padding: 8px;  /* 内边距 */
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);  /* 阴影效果 */
        transition: all 0.3s ease;  /* 过渡效果 */
    }
    .stSelectbox > div > div > div:hover {
        background-color: #FFFBE6;  /* 悬停时的背景色 */
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);  /* 悬停时的阴影 */
    }    
    
    /* 侧边栏成功消息样式 */
    .stSuccess {
        background-color: #D4E6B5;
        color: #4A5A1C;
        border: none;
        padding: 8px;
        border-radius: 5px;
    }
    .stCheckbox {
        margin-bottom: 10px;
        padding: 10px;
        background-color: #FFF8DC;  /* 稍深的淡黄色背景，与页面一致 */
        border: 1px solid #E6B84D;  /* 边框颜色 */
        border-radius: 8px;  /* 圆角 */
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);  /* 阴影效果 */
        transition: all 0.3s ease;  /* 过渡效果 */
    }
    .stCheckbox:hover {
        background-color: #FFFBE6;  /* 悬停时的背景色 */
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);  /* 悬停时的阴影 */
    }
    .stCheckbox label {
        color: #5A4A1C;  /* 深色文字 */
        font-weight: bold;
    }
            
    </style>
""", unsafe_allow_html=True)

##############################################################################
def generate_access_token():
    """生成随机访问令牌"""
    return secrets.token_urlsafe(32)

def update_user_token(username, config):
    """更新用户的访问令牌"""
    # 生成新令牌和过期时间（30天后）
    token = generate_access_token()
    expiry = time.time() + 720 * 3600  # 720小时（30天）后过期
    
    # 直接在用户配置中添加token和expiry
    config['credentials']['usernames'][username]['token'] = token
    config['credentials']['usernames'][username]['expiry'] = expiry
    
    # 保存更新后的配置
    with open('config.yaml', 'w', encoding='utf-8') as file:
        yaml.dump(config, file, allow_unicode=True)
    
    return token

def get_user_token(username, config):
    """获取用户的有效访问令牌，如果过期则生成新的"""
    user_info = config['credentials']['usernames'][username]   
    # 如果用户没有token或expiry字段，生成新的
    if 'token' not in user_info or 'expiry' not in user_info:
        return update_user_token(username, config)
    
    current_time = time.time()
    
    # 如果令牌过期，生成新的
    if current_time > user_info['expiry']:
        print(f"current_time: {current_time},user_info['expiry']: {user_info['expiry']},令牌过期，生成新的令牌")
        return update_user_token(username, config)
    
    return user_info['token']


def refresh_user_token_on_login(username, config):
    """在用户登录时刷新令牌过期时间"""
    try:
        user_info = config['credentials']['usernames'][username]
        current_token = user_info.get('token')
        
        # 如果没有现有token，则生成新的
        if not current_token:
            return update_user_token(username, config)
            
        # 只更新过期时间
        expiry = time.time() + 240 * 3600  # 24小时后过期
        config['credentials']['usernames'][username]['expiry'] = expiry
        
        # 保存更新后的配置
        with open('config.yaml', 'w', encoding='utf-8') as file:
            yaml.dump(config, file, allow_unicode=True)
            
        print(f"用户 {username} 登录时令牌过期时间已更新")
        return current_token
        
    except Exception as e:
        logger.error(f"更新用户 {username} 登录令牌过期时间时发生错误: {str(e)}")
        return get_user_token(username, config)




def get_user_expiry(username, config):
    user_info = config['credentials']['usernames'][username]
    token_expiry_time=time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(user_info['expiry']))
    return token_expiry_time      







def verify_access_token(username, token, config):
    """验证访问令牌是否有效"""
    user_info = config['credentials']['usernames'][username]
    
    if 'token' not in user_info or 'expiry' not in user_info:
        return False
    
    current_time = time.time()
    
    return (user_info['token'] == token and current_time <= user_info['expiry'])


st.markdown("""
<style>
    .divider {
        width: 100%;
        height: 5px;
        background: linear-gradient(to right, #4e54c8, #8f94fb);
        margin: 30px 0;
    }
    .usage-tips {
        background-color: #FFF8DC;
        padding: 15px;
        border-radius: 10px;
        margin: 10px 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .tips-title {
        color: #5A4A1C;
        font-weight: bold;
        font-size: 0.9em;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 6px;
    }
    
    .tips-content {
        color: #8B7355;
        font-size: 0.85em;
        line-height: 1.5;
        margin-bottom: 8px;
    }
    
    .usage-count {
        background: linear-gradient(135deg, #F5E6B3, #E6B84D);
        padding: 8px 12px;
        border-radius: 8px;
        color: #5A4A1C;
        font-weight: bold;
        font-size: 0.85em;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
    }
    
    .vip-notice {
        background: linear-gradient(45deg, #FF8C00, #FFD700);
        color: #000;
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 0.85em;
        font-weight: bold;
        display: flex;
        align-items: center;
        gap: 6px;
        margin-top: 10px;
        box-shadow: 0 2px 4px rgba(255, 140, 0, 0.2);
    }                
</style>
""", unsafe_allow_html=True)

st.markdown("""
    <style>
    /* 隐藏上传标签文本 */
    [data-testid="stFileUploader"] > div:first-child {
        display: none;
    }
    
    /* 上传区域容器 */
    [data-testid="stFileUploader"] {
        width: 100% !important;
        min-width: 280px !important;
        max-width: 100% !important;
        background: linear-gradient(145deg, #FFF9E8, #FFF4DD) !important;
        padding: 18px !important;
        border-radius: 12px !important;
        margin: 0 auto !important;
        border: 1px solid rgba(190, 155, 90, 0.25) !important;
        box-shadow: 
            inset 0 3px 6px rgba(255, 255, 255, 0.95),
            inset 0 -3px 6px rgba(190, 155, 90, 0.15),
            0 2px 8px rgba(190, 155, 90, 0.1) !important;
    }
    
    /* 美化上传区域 */
    [data-testid="stFileUploader"] > section {
        width: 100% !important;
        background: linear-gradient(135deg, #FFFAF0, #FFF5E6) !important;
        border: 2px solid rgba(190, 155, 90, 0.3) !important;
        border-radius: 12px !important;
        padding: 22px !important;
        cursor: pointer !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        margin: 0 auto !important;
        box-shadow: 
            0 6px 15px rgba(190, 155, 90, 0.12),
            inset 0 3px 6px rgba(255, 255, 255, 0.9),
            inset 0 -3px 6px rgba(190, 155, 90, 0.08) !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        position: relative !important;
        z-index: 1 !important;
    }
    
    [data-testid="stFileUploader"] > section:hover {
        transform: translateY(-2px) !important;
        box-shadow: 
            0 8px 20px rgba(190, 155, 90, 0.15),
            inset 0 3px 6px rgba(255, 255, 255, 0.95),
            inset 0 -3px 6px rgba(190, 155, 90, 0.12) !important;
        border-color: rgba(190, 155, 90, 0.4) !important;
    }
    
    /* 上传按钮样式 */
    [data-testid="stFileUploader"] button {
        width: auto !important;
        min-width: 140px !important;
        max-width: 100% !important;
        background: linear-gradient(45deg, #D4B176, #E6B84D) !important;
        color: #FFFFFF !important;
        border: none !important;
        padding: 10px 20px !important;
        border-radius: 8px !important;
        font-weight: 500 !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 
            0 6px 12px rgba(190, 155, 90, 0.25),
            0 3px 6px rgba(190, 155, 90, 0.15),
            inset 0 2px 4px rgba(255, 255, 255, 0.4) !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
        white-space: nowrap !important;
        position: relative !important;
        z-index: 2 !important;
        transform: translateY(0) !important;
    }
    
    [data-testid="stFileUploader"] button:hover {
        transform: translateY(-2px) !important;
        background: linear-gradient(45deg, #E6B84D, #D4B176) !important;
        box-shadow: 
            0 8px 20px rgba(190, 155, 90, 0.3),
            0 4px 10px rgba(190, 155, 90, 0.2),
            inset 0 2px 4px rgba(255, 255, 255, 0.5) !important;
    }
    
    /* 上传区域外框 */
    .uploader-container {
        width: 100% !important;
        background: linear-gradient(145deg, #FFF9E8, #FFF4DD);
        padding: 24px;
        border-radius: 14px;
        box-shadow: 
            0 8px 25px rgba(190, 155, 90, 0.15),
            inset 0 3px 6px rgba(255, 255, 255, 0.95),
            inset 0 -3px 6px rgba(190, 155, 90, 0.12);
        margin: 20px auto;
        min-width: 250px !important;
        max-width: 100% !important;
        border: 1px solid rgba(190, 155, 90, 0.25);
        box-sizing: border-box !important;
        position: relative !important;
    }
    
    </style>
    
""", unsafe_allow_html=True)



st.markdown("""
    <style>
    .uploadInfo {
        color: #907860;
        font-size: 14px;
        text-align: left; /* 更改为左对齐 */
        margin-bottom: 12px;
        line-height: 1.5;
    }
    </style>
""", unsafe_allow_html=True)
###############################################################################################
# 删除视频文件
###############################################################################################
def delete_video_files(username, video_name):
    # 删除相关文件
    base_name = os.path.splitext(video_name)[0]
    files_to_delete = [
        os.path.join(VIDEO_BASE_PATH, video_name),  # mp4文件
        os.path.join(VIDEO_BASE_PATH, f"{base_name}.json"),  # json文件
        os.path.join(VIDEO_BASE_PATH, f"{base_name}.srt")   # srt文件
    ]
    
    for file_path in files_to_delete:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
            except Exception as e:
                st.error(f"删除文件失败: {file_path}, 错误: {str(e)}")

    # 更新 user_videos.json
    try:
        with open('user_videos.json', 'r', encoding='utf-8') as f:
            user_videos = json.load(f)
        
        if username in user_videos and video_name in user_videos[username]:
            user_videos[username].remove(video_name)
            
            with open('user_videos.json', 'w', encoding='utf-8') as f:
                json.dump(user_videos, f, indent=2, ensure_ascii=False)
            
            return True
    except Exception as e:
        st.error(f"更新用户视频记录失败: {str(e)}")
        return False
#########################################################################################    
def load_tool_visits():
    """从文件加载工具访问次数"""
    try:
        with open('tool_visits.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        # 如果文件不存在或格式错误，返回默认值
        default_tools = {
            'ai_demo': 0,
            'aiinpaint': 0,
            'paint_board': 0,
            'florence': 0
        }
        # 创建文件并保存默认值
        with open('tool_visits.json', 'w', encoding='utf-8') as f:
            json.dump(default_tools, f, indent=2, ensure_ascii=False)
        return default_tools
    except Exception as e:
        print(f"Error loading tool visits: {e}")
        return {
            'ai_demo': 0,
            'aiinpaint': 0,
            'paint_board': 0,
            'florence': 0
        }





# 创建AI工具展示区域


def create_ai_tool_section():
    """创建AI工具展示区域"""
    # 获取工具访问次数
    # 直接从文件加载工具访问次数
    tool_visits = load_tool_visits()
    print("Tool visits data:", tool_visits)  # 调试日志  
    # 获取用户信息和token
    username = st.session_state.username
    password = get_user_password(username)
    config = load_config()
    token = get_user_token(username, config)
    origin = get_origin()
    
    # 构建完整URL
    ai_demo_url = f"{origin}/api/ai-demo?user={username}&key={password}&token={token}"
    ai_inpaint_url = f"{origin}/api/aiinpaint?user={username}&key={password}&token={token}"
    electric_demo_url = f"{origin}/api/mermindfig/electric.html?user={username}&key={password}&token={token}"
    latex_editor_url = f"{origin}/api/mermindfig/latexeditor.html?user={username}&key={password}&token={token}"
    markdown_renderer_url = f"{origin}/api/mermindfig/markdown-renderer.html?user={username}&key={password}&token={token}"
    circuit_url = f"{origin}/api/circuit?user={username}&key={password}&token={token}"
    ai_board_url = f"{origin}/api/paint-board?user={username}&key={password}&token={token}"
    florence2_url = f"{origin}/api/florence?user={username}&key={password}&token={token}"
    onnxdemo_url = f"{origin}/api/onnxdemo?user={username}&key={password}&token={token}"
    html_content = f"""
        <style>
            /* 标题样式优化 */
            .section-title {{
                text-align: center;
                font-size: 2em;
                font-weight: 800;
                margin: 40px 0 30px;
                padding: 0;
                background: linear-gradient(135deg, #5A4A1C, #8B7355);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                letter-spacing: 0.05em;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
                position: relative;
                pointer-events: none;
                user-select: none;
            }}
            /* 隐藏链接图标 */
            .section-title a {{
                display: none; /* 隐藏链接图标 */
            }}
            .section-title::after {{
                content: '';
                display: block;
                width: 60px;
                height: 3px;
                background: linear-gradient(135deg, #E6B84D, #D4B176);
                margin: 15px auto 0;
                border-radius: 2px;
            }}

            /* 工具容器样式优化 - 修改为两行三列固定布局 */
            .tools-container {{
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                grid-template-rows: repeat(2, auto);
                gap: 25px;
                padding: 30px;
                max-width: 1200px;
                margin: 0 auto;
                box-sizing: border-box;
                max-height: 800px;
                overflow-y: auto;
                overflow-x: hidden;
            }}

            /* 滚动条样式 */
            .tools-container::-webkit-scrollbar {{
                width: 8px;
            }}

            .tools-container::-webkit-scrollbar-track {{
                background: #f1f1f1;
                border-radius: 10px;
            }}

            .tools-container::-webkit-scrollbar-thumb {{
                background: #D4B176;
                border-radius: 10px;
            }}

            .tools-container::-webkit-scrollbar-thumb:hover {{
                background: #E6B84D;
            }}

            /* 工具卡片样式优化 */
            .tool-card {{
                position: relative;
                background: linear-gradient(145deg, #ffffff, #fafafa);
                padding: 30px 25px;
                border-radius: 20px;
                border: 1px solid rgba(230, 184, 77, 0.15);
                box-shadow: 
                    0 10px 20px rgba(0,0,0,0.05),
                    0 6px 6px rgba(0,0,0,0.02),
                    inset 0 -2px 5px rgba(255,255,255,0.7);
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 18px;
                cursor: pointer;
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                overflow: hidden;
                height: 300px;  /* 设置固定高度 */
                width: 300px;  /* 设置固定宽度 */
            }}

            .tool-card:not(.coming-soon):hover {{
                transform: translateY(-8px);
                box-shadow: 
                    0 20px 40px rgba(0,0,0,0.08),
                    0 10px 10px rgba(0,0,0,0.04),
                    inset 0 -2px 5px rgba(255,255,255,0.7);
                border-color: rgba(230, 184, 77, 0.3);
            }}

            /* 标签样式优化 */
            .tool-tag {{
                position: absolute;
                top: 15px;
                right: 15px;
                padding: 6px 14px;
                border-radius: 25px;
                font-size: 0.85em;
                font-weight: 600;
                letter-spacing: 0.03em;
                box-shadow: 
                    0 4px 8px rgba(0,0,0,0.1),
                    inset 0 1px 3px rgba(255,255,255,0.3);
                z-index: 1;
            }}

            .tool-tag.free {{
                background: linear-gradient(135deg, #4CAF50, #45a049);
                color: white;
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            }}

            .tool-tag.coming-soon {{
                background: linear-gradient(135deg, #FFA726, #FB8C00);
                color: white;
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            }}

            /* 图标样式优化 */
            .tool-icon {{
                font-size: 3em;
                margin: 5px 0;
                filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
                transition: transform 0.3s ease;
            }}

            .tool-card:hover .tool-icon {{
                transform: scale(1.1);
            }}

            /* 标题和描述样式优化 */
            .tool-title {{
                color: #5A4A1C;
                font-weight: 700;
                font-size: 1.3em;
                margin: 0;
                text-align: center;
                width: 100%;
                padding: 0 10px;
                letter-spacing: 0.02em;
            }}

            .tool-description {{
                color: #8B7355;
                font-size: 0.95em;
                text-align: center;
                margin: 0;
                line-height: 1.5;
                width: 100%;
                padding: 0 10px;
            }}

            /* 访问次数样式 */
            .usage-visit-count {{
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
                background: linear-gradient(135deg, #F5E6B3, #E6B84D);
                padding: 6px 12px;
                border-radius: 20px;
                color: #5A4A1C;
                font-size: 0.85em;
                font-weight: 600;
                box-shadow: 
                    0 2px 4px rgba(0,0,0,0.1),
                    inset 0 1px 2px rgba(255,255,255,0.5);
                margin-top: 5px;
                width: auto;
            }}

            .electromagnetism-card {{
                /* border: 2px solid #228be6; */
                box-shadow: 0 4px 16px rgba(34, 139, 230, 0.15);
                transition: box-shadow 0.3s, transform 0.3s;
            }}
            .electromagnetism-card:hover {{
                box-shadow: 0 8px 32px rgba(34, 139, 230, 0.25);
                transform: translateY(-4px) scale(1.03);
            }}
            .electromagnetism-icon {{
                margin: 0 auto 8px auto;
                display: flex;
                justify-content: center;
                align-items: center;
            }}
            .tool-title {{
                color: #1864ab;
                font-weight: bold;
                font-size: 1.2em;
            }}
            .tool-description {{
                color: #495057;
            }}





        </style>

        <div class="knowledge-title">AI 工具展示</div>

        <div class="browser-tips" style="margin: 20px 0; padding: 15px; background-color: #FAF3E0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
            <div class="browser-recommendation" style="margin-bottom: 15px;">
                <div class="info-icon" style="display: inline-block; vertical-align: middle; margin-right: 10px;">
                    <i class="fas fa-info-circle" style="color: #FFB74D;"></i> <!-- 使用柔和的橙色 -->
                </div>
                <div class="browser-content" style="display: inline-block; vertical-align: middle; color: #5A4A1C;"> <!-- 深色文字 -->
                    推荐使用 
                    <span class="browser-icon" style="margin: 0 5px;">
                        <i class="fab fa-chrome" style="color: #4285F4;"></i> Chrome
                    </span> 
                    或 
                    <span class="browser-icon" style="margin: 0 5px;">
                        <i class="fab fa-edge" style="color: #0078D7;"></i> Edge
                    </span> 
                    浏览器访问
                </div>
            </div>
            <div class="model-loading-tips">
                <div class="loading-step" style="margin-bottom: 10px; color: #5A4A1C;"> <!-- 深色文字 -->
                    <span class="first-load" style="font-weight: bold;">
                        <i class="fas fa-clock" style="color: #FFB74D;"></i> 首次使用
                    </span> 
                    可能需要加载模型，建议从 
                    <a href="https://xpuai.20140806.xyz" target="_blank" class="hf-link" style="color: #4A4A1C; text-decoration: underline;">
                        <i class="fas fa-link"></i> https://xpuai.20140806.xyz
                    </a>
                    域名访问 
                    <span class="loading-time" style="color: #8B7355;">(约3-5分钟)</span>
                </div>
                <div class="loading-step" style="color: #5A4A1C;"> <!-- 深色文字 -->
                    <span class="subsequent-use" style="font-weight: bold;">
                        <i class="fas fa-bolt" style="color: #FFB74D;"></i> 后续使用
                    </span> 
                    无需重新加载，可直接使用
                </div>
            </div>
        </div>


        <div class="tools-container">
            <a href="{ai_demo_url}" 
               target="_blank" 
               style="text-decoration: none; display: block; width: 100%;">
                <div class="tool-card" id="tool-0">
                    <span class="tool-tag free">✨实用工具</span>
                    <div class="tool-icon">✂️🖼️</div>
                    <div class="tool-title">AI 去背景</div>
                    <p class="tool-description">智能去背景，生成证件照</p>
                    <div class="usage-visit-count">
                        <span>👀</span>
                        <span>{tool_visits.get('ai_demo', 0)} 次访问</span>
                    </div>
                </div>
            </a>  
            <a href="{electric_demo_url}" 
            target="_blank" 
            style="text-decoration: none; display: block; width: 100%;">
            <div class="tool-card electromagnetism-card" id="tool-1">
                <span class="tool-tag free">✨学科工具</span>
                <div class="tool-icon electromagnetism-icon" style="display: flex; flex-direction: column; align-items: center; justify-content: center; font-size: 3em;">
                    🧲⚡🌊
                </div>
                <div class="tool-title">电磁学可视化演示</div>
                <p class="tool-description">电场与磁场的动态可视化，探索电磁现象的本质</p>
                <div class="usage-visit-count">
                    <span>👀</span>
                    <span>{tool_visits.get('electric', 0)} 次访问</span>
                </div>
            </div>
            </a>
            <a href="{latex_editor_url}" 
               target="_blank" 
               style="text-decoration: none; display: block; width: 100%;">
                <div class="tool-card" id="tool-7">
                    <span class="tool-tag free">✨开源工具</span>
                    <div class="tool-icon">🧮📝</div>
                    <div class="tool-title">Latex公式编辑器</div>
                    <p class="tool-description">开源Latex公式编辑器</p>
                    <div class="usage-visit-count">
                        <span>👀</span>
                        <span>{tool_visits.get('latex_editor', 0)} 次访问</span>
                    </div>
                </div>
            </a> 
            <a href="{markdown_renderer_url}" 
               target="_blank" 
               style="text-decoration: none; display: block; width: 100%;">
                <div class="tool-card" id="tool-8">
                    <span class="tool-tag free">✨学科工具</span>
                    <div class="tool-icon">📝🖼️</div>
                    <div class="tool-title">Markdown渲染器</div>
                    <p class="tool-description">Markdown渲染器</p>
                    <div class="usage-visit-count">
                        <span>👀</span>
                        <span>{tool_visits.get('markdown_renderer', 0)} 次访问</span>
                    </div>
                </div>
            </a> 
            <a href="{circuit_url}" 
               target="_blank" 
               style="text-decoration: none; display: block; width: 100%;">
                <div class="tool-card" id="tool-6">
                    <span class="tool-tag free">✨开源工具</span>
                    <div class="tool-icon">⚡🔌</div>
                    <div class="tool-title">电路仿真</div>
                    <p class="tool-description">电路仿真，体验电路</p>
                    <div class="usage-visit-count">
                        <span>👀</span>
                        <span>{tool_visits.get('circuit', 0)} 次访问</span>
                    </div>
                </div>
            </a>     
            <a href="{ai_board_url}" 
               target="_blank" 
               style="text-decoration: none; display: block; width: 100%;">
                <div class="tool-card" id="tool-2">
                    <span class="tool-tag free">✨实用工具</span>
                    <div class="tool-icon">🎨💡</div>
                    <div class="tool-title">创意画板</div>
                    <p class="tool-description">灵感涌现，自由创作</p>
                    <div class="usage-visit-count">
                        <span>👀</span>
                        <span>{tool_visits.get('paint_board', 0)} 次访问</span>
                    </div>
                </div>
            </a> 
            <a href="{florence2_url}" 
               target="_blank" 
               style="text-decoration: none; display: block; width: 100%;">
                <div class="tool-card" id="tool-3">
                    <span class="tool-tag free">✨AI体验</span>
                    <div class="tool-icon">🖼️🔍</div>
                    <div class="tool-title">图片识别</div>
                    <p class="tool-description">智能识别，体验AI</p>
                    <div class="usage-visit-count">
                        <span>👀</span>
                        <span>{tool_visits.get('florence', 0)} 次访问</span>
                    </div>
                </div>
            </a> 
            <a href="{onnxdemo_url}" 
               target="_blank" 
               style="text-decoration: none; display: block; width: 100%;">
                <div class="tool-card" id="tool-4">
                    <span class="tool-tag free">✨AI体验</span>
                    <div class="tool-icon">🤖👁️</div> 
                    <div class="tool-title">onnx视觉模型</div>
                    <p class="tool-description">ai眼中的世界</p>
                    <div class="usage-visit-count">
                        <span>👀</span>
                        <span>{tool_visits.get('onnxdemo', 0)} 次访问</span>
                    </div>
                </div>
            </a>             
            <div class="tool-card coming-soon" id="tool-5">
                <span class="tool-tag coming-soon">🛠️开发中</span>
                <div class="tool-icon">🎨🖌️🤖</div>
                <div class="tool-title">AI 绘画</div>
                <p class="tool-description">即将推出 - 智能AI绘画工具</p>
            </div>
        </div>     
    """

    st.markdown("""
        <head>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
        </head>
    """, unsafe_allow_html=True)    
    # 渲染HTML
    st.markdown(html_content, unsafe_allow_html=True)
###############################################################################################


st.markdown("""
<style>
/* 播放按钮样式 */
div.stButton > button[kind="secondary"] {
    background: linear-gradient(135deg, #4CAF50, #45a049) !important;
    color: white !important;
    border: none !important;
    padding: 0.6em 1.2em !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
    letter-spacing: 0.5px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 
        0 2px 4px rgba(69, 160, 73, 0.2),
        inset 0 1px 1px rgba(255, 255, 255, 0.2) !important;
    position: relative !important;
    overflow: hidden !important;
}

/* 播放按钮悬停效果 */
div.stButton > button[kind="secondary"]:hover {
    background: linear-gradient(135deg, #45a049, #357a38) !important;
    transform: translateY(-2px) !important;
    box-shadow: 
        0 4px 12px rgba(69, 160, 73, 0.3),
        inset 0 1px 1px rgba(255, 255, 255, 0.3) !important;
}

/* 删除按钮样式 */
div.stButton > button[kind="primary"] {
    background: linear-gradient(135deg, #ff4d4d, #cc0000) !important;
    color: white !important;
    border: none !important;
    padding: 0.6em 1.2em !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
    letter-spacing: 0.5px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 
        0 2px 4px rgba(204, 0, 0, 0.2),
        inset 0 1px 1px rgba(255, 255, 255, 0.2) !important;
    position: relative !important;
    overflow: hidden !important;
}

/* 删除按钮悬停效果 */
div.stButton > button[kind="primary"]:hover {
    background: linear-gradient(135deg, #cc0000, #990000) !important;
    transform: translateY(-2px) !important;
    box-shadow: 
        0 4px 12px rgba(204, 0, 0, 0.3),
        inset 0 1px 1px rgba(255, 255, 255, 0.3) !important;
}

/* 确认删除按钮样式 */
div.stButton > button[kind="confirm"] {
    background: linear-gradient(135deg, #ff6b6b, #ff5252) !important;
    color: white !important;
    border: none !important;
    padding: 0.6em 1.2em !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
    letter-spacing: 0.5px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 
        0 2px 4px rgba(255, 82, 82, 0.2),
        inset 0 1px 1px rgba(255, 255, 255, 0.2) !important;
}

/* 确认删除按钮悬停效果 */
div.stButton > button[kind="confirm"]:hover {
    background: linear-gradient(135deg, #ff5252, #ff4242) !important;
    transform: translateY(-2px) !important;
    box-shadow: 
        0 4px 12px rgba(255, 82, 82, 0.3),
        inset 0 1px 1px rgba(255, 255, 255, 0.3) !important;
}

/* 取消按钮样式 */
div.stButton > button[kind="cancel"] {
    background: linear-gradient(135deg, #78909c, #607d8b) !important;
    color: white !important;
    border: none !important;
    padding: 0.6em 1.2em !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
    letter-spacing: 0.5px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 
        0 2px 4px rgba(96, 125, 139, 0.2),
        inset 0 1px 1px rgba(255, 255, 255, 0.2) !important;
}

/* 取消按钮悬停效果 */
div.stButton > button[kind="cancel"]:hover {
    background: linear-gradient(135deg, #607d8b, #546e7a) !important;
    transform: translateY(-2px) !important;
    box-shadow: 
        0 4px 12px rgba(96, 125, 139, 0.3),
        inset 0 1px 1px rgba(255, 255, 255, 0.3) !important;
}


            

    

</style>
""", unsafe_allow_html=True)




def display_main_page():



    # 获取 API 密钥
    api_key, api_key_type = get_api_key()
    
    # 根据类型正确设置对应的 API key,更新api_key_deepseek,api_key_siliconflow,api_key_qwen  
    if api_key_type == "deepseek":
        api_key_deepseek = api_key
        api_key_siliconflow = st.session_state.get('api_key_siliconflow', '')
        api_key_qwen = st.session_state.get('api_key_qwen', '')
    elif api_key_type == "qwen":
        api_key_qwen = api_key
        api_key_deepseek = st.session_state.get('api_key_deepseek', '')
        api_key_siliconflow = st.session_state.get('api_key_siliconflow', '')
    else:
        api_key_siliconflow = api_key
        api_key_deepseek = st.session_state.get('api_key_deepseek', '')
        api_key_qwen = st.session_state.get('api_key_qwen', '')

    print(f"api_key_deepseek is:{api_key_deepseek},api_key_siliconflow is:{api_key_siliconflow},api_key_qwen is:{api_key_qwen}")
    # 保存当前的 API keys 到 session_state
    st.session_state.api_key_deepseek = api_key_deepseek
    st.session_state.api_key_siliconflow = api_key_siliconflow
    st.session_state.api_key_qwen = api_key_qwen
    api_key_rag = api_key_model



    st.markdown("""
        <style>
        .centered-title {
            color: #FFB74D;  /* 深色文字 */
            background-color: #FFF8DC;  /* 稍深的淡黄色背景 */
            padding: 20px;  /* 减少内边距 */
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);  /* 更明显的阴影效果 */
            font-weight: bold;
            text-align: center;
            font-size: 36px;  /* 增加字体大小 */
            margin: 0.1px auto 20px;  /* 进一步减少顶部间距 */
            max-width: 800px;  /* 限制最大宽度 */
            border: 2px solid #D4B176; /* 添加边框以增强视觉效果 */
        }
        </style>
    """, unsafe_allow_html=True)
    st.markdown('<div class="centered-title">AI 助教</div>', unsafe_allow_html=True)
    initialize_user_config()
    if 'visit_count' not in st.session_state:
        st.session_state['visit_count'] = load_visit_count()
    client_ip = get_forwarded_ip()
    print(client_ip)

    weather_info = get_weather(ip_address=client_ip)
    weather_html = render_weather_html(weather_info)
    # 使用 components.html 来渲染天气信息，添加响应式宽度和Streamlit风格的CSS
    st.components.v1.html(f"""
        <!DOCTYPE html>
        <html>
        <head>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
            <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
        </head>
        <body>
            {weather_html}
        </body>
        </html>
    """, height=400)  # 调整高度为400px，以适应动态内容
    st.markdown("---")  # 添加分隔线   
    # 创建两列布局
    col1, col2, col3 = st.columns([1,3,3])

    with col3:
        st.markdown('<div>', unsafe_allow_html=True)
        if client_ip:
            st.session_state['X-Forwarded-For'] = client_ip
            st.markdown('<div class="ip-address-title">访问者的 IP 地址是:</div>', unsafe_allow_html=True)
            st.markdown(f'<div class="ip-address-value">{client_ip}</div>', unsafe_allow_html=True)
        else:
            st.markdown('<div class="ip-address-title">无法获取访问者的 IP 地址。</div>', unsafe_allow_html=True)
        st.markdown('</div>', unsafe_allow_html=True)

    with col2:
        # 增加访问计数并显示
        st.session_state['visit_count'] += 1
        st.markdown('<div>', unsafe_allow_html=True)
        st.markdown('<div class="visit-count-title">总访问量:</div>', unsafe_allow_html=True)
        st.markdown(f'<div class="visit-count-number">{st.session_state["visit_count"]}</div>', unsafe_allow_html=True)
        st.markdown('</div>', unsafe_allow_html=True)
    # 保存访问计数
    save_visit_count(st.session_state['visit_count'])




    # 定义背景图片的路径
    background_logo_path = os.path.join(SCRIPT_DIR, "pic", "xpulogo.png")
    background_image_path = os.path.join(SCRIPT_DIR, "pic", "bn_dt_img1.jpg")
    
    logo_base64 = get_image_as_base64(background_logo_path)
    bg_image_base64 = get_image_as_base64(background_image_path)

    st.markdown(center_image(logo_base64, "png"), unsafe_allow_html=True)
    st.markdown(center_image(bg_image_base64, "jpeg"), unsafe_allow_html=True)
    
 ##################################################################################   
    # 在主函数或相关位置调用 get_origin

    username = st.session_state.username
    password = get_user_password(username)
    origin = get_origin()
    # 检查用户 VIP 状态
    is_vip = False
    try:
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            if st.session_state.username in config['credentials']['usernames']:
                user_info = config['credentials']['usernames'][st.session_state.username]
                if 'vip_expiry' in user_info and user_info['vip_expiry'] > time.time():
                    is_vip = True
    except Exception as e:
        logging.error(f"Error checking VIP status: {str(e)}")


    view_blog_colors = get_random_color_scheme()
    view_blog_colors['name'] = 'viewBlog'

    ai_demo_colors = get_random_color_scheme()
    ai_demo_colors['name'] = 'aidemo'

    write_blog_colors = get_random_color_scheme()
    write_blog_colors['name'] = 'writeBlog'

    config = load_config()
    token = get_user_token(username, config)
    print(f"token is:{token},token expiry time is:{get_user_expiry(username, config)}")

    button_schemes = [
        {
            'name': 'viewBlog',
            'gradient': ['#43cea2', '#185a9d'],  # 绿蓝渐变
            'color': '#fff',
            'icon': '📚',
            'text': '访问 Blog',
            'url': f"{origin}/api/blog_list/?user={username}&key={password}&token={token}"
        },
        {
            'name': 'courseVideo',
            'gradient': ['#ff9966', '#ff5e62'],  # 橙红渐变
            'color': '#fff',
            'icon': '🎬',
            'text': '访问 课程视频',
            'url': f"{origin}/api/collections_page?user={username}&key={password}&token={token}"
        },
        {
            'name': 'writeBlog',
            'gradient': ['#7f53ac', '#657ced'],  # 紫蓝渐变
            'color': '#fff',
            'icon': '✍️',
            'text': '编写 Blog',
            'url': f"{origin}/blog/?user={username}&key={password}&token={token}"
        },
        {
            'name': 'aiNote',
            'gradient': ['#ff5858', '#f09819'],  # 红橙渐变
            'color': '#fff',
            'icon': '📝',
            'text': '进入 AI笔记',
            'url': f"{origin}/api/mermindfig/chat_log_web/chat_viewer.html?user={username}&key={password}&token={token}"
        }
    ]
    st.markdown("""
        <style>
        .main-entry-row {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 32px;
            margin: 32px 0 36px 0;
        }
        .main-entry-btn {
            min-width: 180px;
            min-height: 64px;
            border-radius: 18px;
            font-size: 1.25em;
            font-weight: 700;
            box-shadow: 0 6px 24px rgba(80,80,80,0.13), 0 1.5px 4px rgba(0,0,0,0.08);
            border: none;
            outline: none;
            cursor: pointer;
            transition: transform 0.18s cubic-bezier(.4,2,.6,1), box-shadow 0.18s;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 32px;
            letter-spacing: 0.02em;
            text-decoration: none !important;
            position: relative;
            overflow: hidden;
        }
        .main-entry-btn:hover {
            transform: scale(1.06);
            box-shadow: 0 12px 32px rgba(80,80,80,0.18), 0 2px 8px rgba(0,0,0,0.12);
            filter: brightness(1.08);
        }
        .main-entry-btn .main-entry-icon {
            font-size: 1.6em;
            margin-right: 12px;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.08));
        }
        </style>
    """, unsafe_allow_html=True)
    st.markdown('<div class="main-entry-row">', unsafe_allow_html=True)
    for scheme in button_schemes:
        st.markdown(f'''
            <a href="{scheme['url']}" target="_blank" class="main-entry-btn" style="background: linear-gradient(45deg, {scheme['gradient'][0]}, {scheme['gradient'][1]}); color: {scheme['color']};">
                <span class="main-entry-icon">{scheme['icon']}</span>{scheme['text']}
            </a>
        ''', unsafe_allow_html=True)
    st.markdown('</div>', unsafe_allow_html=True)






    # 在适当的位置添加AI工具区域
    st.markdown("---")  # 添加分隔线
    create_ai_tool_section()
    st.markdown("---")  # 添加分隔线
 ##################################################################################   
    # 添加明显的分界线

 ################################################################################## 

    # 使用自定义样式
    # st.sidebar.markdown('<div class="sidebar-divider"></div>', unsafe_allow_html=True)




    if st.session_state.username == "admin":
        admin_url = f"{origin}/api/blog_list_root/?user={username}&key={password}&token={token}"
        admin_dashboard_url = f"{origin}/api/admin_dashboard/?user={username}&key={password}&token={token}"
        admin_classes_url = f"{origin}/api/class_user_data/class_user_data.html?user={username}&key={password}&token={token}"
        st.sidebar.markdown(f'<a href="{admin_url}" class="sidebar-link">管理员页面</a>', unsafe_allow_html=True)
        st.sidebar.markdown(f'<a href="{admin_classes_url}" class="sidebar-link">班级学习统计</a>', unsafe_allow_html=True)   
        st.sidebar.markdown(f'<a href="{admin_dashboard_url}" class="sidebar-link">管理员仪表盘</a>', unsafe_allow_html=True) 

    # st.sidebar.markdown('<div class="sidebar-title">Deepseek API 应用</div>', unsafe_allow_html=True)    






    if not api_key:
        st.warning("请在侧边栏输入有效的 Deepseek API 密钥。")
        return
    # st.sidebar.markdown('<div class="sidebar-divider"></div>', unsafe_allow_html=True)





    st.sidebar.markdown('<div class="sidebar-title">用户反馈</div>', unsafe_allow_html=True)
    
    # 初始化 session_state
    if 'user_feedback' not in st.session_state:
        st.session_state.user_feedback = ""
    if 'feedback_submitted' not in st.session_state:
        st.session_state.feedback_submitted = False

    # 使用 key 参数来链接输入框和 session_state
    st.sidebar.text_area("请输入您的反馈：", height=100, key="user_feedback")
    
    st.sidebar.button("提交反馈", on_click=submit_feedback)

    if st.session_state.feedback_submitted:
        st.sidebar.success("感谢您的反馈！")
        st.session_state.feedback_submitted = False

    st.sidebar.markdown('<div class="sidebar-divider"></div>', unsafe_allow_html=True)
    # 添加使用提示
    st.sidebar.markdown("""
        <div class="usage-tips">
            <div class="tips-title">
                <span>💡</span> 使用提示
            </div>
            <div class="tips-content">
                • 使用次数每日00:00更新<br>
                • 普通用户需输入正确的API密钥解锁全部功能<br>
                • 支持中英文对话<br>
                • 支持联网搜索回答
            </div>
        <div class="tips-title">
            <span>🔄</span> 更新提示
        </div>
        <div class="tips-content">
            • 2024-11-16: 生成方法新增润色功能，只需输入需要润色的段落和选择需要参考的论文，其它参数保持默认<br>
            • 2024-11-16: 必须上传参考论文到 ragdoc 目录，才能正常使用润色功能<br>
            • 2024-11-16: 可上传选择多篇参考论文，润色功能支持中英文论文<br>
            • 2025-02-26: 增加深度搜索功能，本地深度搜索根据任务需要8分钟,联网需要20分钟，超时搜索内容会被截断<br>
        </div>                        
        </div>
    """.format(
        '<div class="vip-notice"><span>♛</span> VIP用户无需输入API密钥</div>' if is_vip else ''
    ), unsafe_allow_html=True)


    video_name={}
    video_name = st.query_params.get("video")
    # 添加一些自定义 CSS 来调整整体布局
    # 使用列布局来控制 file_uploader 的位置
    st.markdown('<div class="knowledge-title">视频对话</div>', unsafe_allow_html=True) 
    col1, col3 = st.columns([2,2])
    # 创建两列布局来放置按钮
    col_play, col_del = st.columns([ 1, 1])
    with col1:             
        remaining_uploads = manage_user_upload_count(st.session_state.username)
        st.markdown(f'<div class="uploadInfo">剩余上传次数: {remaining_uploads}</div>', unsafe_allow_html=True)
        if remaining_uploads > 0:
            st.markdown("""
                <div style="text-align: center; color: #8B7355; font-size: 0.9em; margin-bottom: 10px;">
                    <span style="background: linear-gradient(45deg, #E6B84D, #FFD700); 
                            -webkit-background-clip: text; 
                            -webkit-text-fill-color: transparent;">
                        📤 点击或拖放上传视频文件 (MP4)
                    </span>
                </div>
            """, unsafe_allow_html=True)
            uploaded_file = st.file_uploader("上传视频", type=["mp4"], key="video_uploader",label_visibility="collapsed")
            st.markdown('<p class="uploadInfo">提示：上传视频文件名只支持英文_数字组合，否则以当前时间重新命名</p>', unsafe_allow_html=True)
        else:
            st.warning("已达到最大上传次数限制。")
            uploaded_file = None

        st.markdown('</div>', unsafe_allow_html=True)
    # 在主程序中使用更新后的函数
    if uploaded_file is not None and manage_user_upload_count(st.session_state.username) > 0:
        file_name = uploaded_file.name
        file_size = uploaded_file.size        
        if file_size > 1024 * 1024 * 1024:  # 1GB
            st.error("文件大小超过1GB，请上传小于1GB的文件。")
        else:
            st.info(f"正在上传文件 '{file_name}'，请稍候...")
            
            upload_success, message, server_filename = upload_file_to_server(uploaded_file,username)
            
            if upload_success:
                st.success(f"文件 '{file_name}' 上传成功！加入排队生成中英字幕序列，3-10min后刷新页面显示中英字幕")
                video_name = server_filename or file_name  # 使用服务器返回的文件名，如果没有则使用原文件名
                record_user_video(st.session_state.username, video_name)  # 记录用户上传的视频
                remaining_uploads = manage_user_upload_count(st.session_state.username, increment=True)
                st.markdown(f'<div class="uploadInfo">剩余上传次数: {remaining_uploads}</div>', unsafe_allow_html=True)
            else:
                if message == "文件已存在":
                    st.warning(f"文件 '{file_name}' 已存在于服务器上。")
                    video_name = server_filename or file_name  # 如果文件已存在，使用服务器返回的文件名
                    record_user_video(st.session_state.username, video_name)  # 记录用户上传的视频
                    remaining_uploads = manage_user_upload_count(st.session_state.username, increment=True)

                    st.markdown(f'<div class="uploadInfo">剩余上传次数: {remaining_uploads}</div>', unsafe_allow_html=True)
                else:
                    st.error(f"文件上传失败: {message}")
                    st.error("请检查服务器状态并重试。")

    elif manage_user_upload_count(st.session_state.username) <= 0:
        st.warning("已达到最大上传次数限制。")


    
    # 修改选择已有视频文件部分
    with col3:

        # 初始化session状态
        if 'video_name_play' not in st.session_state:
            st.session_state.video_name_play = None
        if 'start_video_chat' not in st.session_state:
            st.session_state.start_video_chat = False
        if 'conversation_id' not in st.session_state:
            st.session_state.conversation_id = ""

        # 获取用户视频列表
        mp4_files = get_user_mp4_files(st.session_state.username)
    
        # 视频选择框
        selected_video = st.selectbox(
            label="选择视频",
            options=[""] + mp4_files,
            format_func=lambda x: '选择视频' if x == "" else x,
            label_visibility="collapsed"  # 隐藏标签但保持可访问性
        )



        if 'sent_state' not in st.session_state:
            st.session_state.sent_state = True


        if 'selected_video' not in st.session_state:
            st.session_state.selected_video = selected_video


        if selected_video != st.session_state.selected_video:
            st.session_state.sent_state = False
            st.session_state.start_video_chat=False

        if 'delete_confirm' not in st.session_state:
            st.session_state.delete_confirm = False
        # 当选择框为空时的处理
        if selected_video == "":
            st.session_state.video_name_play = None
            st.session_state.start_video_chat = False
            st.session_state.conversation_id = ""
            st.markdown("""
                <div class="custom-info">
                    <div class="info-content">
                        <span class="info-icon">🎬</span>
                        <span class="info-text">请选择一个视频进行播放或删除</span>
                    </div>
                </div>
                <style>
                    .custom-info {
                        background: linear-gradient(135deg, #FFF8DC, #FFEFD5);
                        border: 1px solid rgba(230, 184, 77, 0.3);
                        border-radius: 10px;
                        padding: 5px 10px;
                        margin: 10px 0;
                        color: #8B7355;
                        font-size: 0.95em;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    .info-content {
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    }
                    .info-icon {
                        font-size: 1.2em;
                    }
                    .info-text {
                        font-weight: 500;
                    }
                </style>
            """, unsafe_allow_html=True)





        elif selected_video:
            # 添加自定义CSS样式
            st.markdown("""
                <style>
                    /* 视频选择框样式 */
                    .video-selection-container {
                        display: inline-flex;
                        align-items: center;
                        background: linear-gradient(135deg, #FFF8DC, #FFFDF0);
                        border: 1px solid rgba(230, 184, 77, 0.3);
                        border-radius: 12px;
                        padding: 0.8rem 1.2rem;
                        margin: 1rem auto;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
                    }
                    
                    .video-icon {
                        color: #E6B84D;
                        font-size: 1.2em;
                        margin-right: 10px;
                    }
                    
                    .video-name {
                        color: #8B7355;
                        font-weight: 500;
                        font-size: 1.1em;
                        word-break: break-all;
                    }                   
                </style>
            """, unsafe_allow_html=True)
            
            # 显示已选择视频
            st.markdown(f"""
                <div class="video-selection-container">
                    <span class="video-icon">🎬</span>
                    <span class="video-name">{selected_video}</span>
                </div>
            """, unsafe_allow_html=True)           



            # 播放按钮
            if st.button(
                "🎥 播放视频",
                use_container_width=True,
                key="play_button",
                type="secondary",
                args=('secondary',)  # 添加kind属性
            ):
                st.session_state.video_name_play = selected_video
                st.session_state.selected_video = selected_video
                st.session_state.sent_state = True
                st.session_state.start_video_chat = False
                st.session_state.conversation_id = ""
                st.rerun()

            # 删除按钮
            if not st.session_state.delete_confirm:
                if st.button(
                    "🗑️ 删除视频",
                    use_container_width=True,
                    key="delete_button",
                    type="primary",
                    args=('primary',)  # 添加kind属性
                ):
                    st.session_state.delete_confirm = True
                    st.rerun()
            else:
                st.warning("⚠️ 确定要删除这个视频吗？此操作无法撤销。")
                
                # 确认和取消按钮
                confirm_col1, confirm_col2 = st.columns(2)
                
                with confirm_col1:
                    if st.button(
                        "✔️ 确认删除",
                        use_container_width=True,
                        key="confirm_delete_button",
                        type="primary",
                        args=('confirm',)  # 使用confirm类型
                    ):
                        try:
                            if delete_video_files(st.session_state.username, selected_video):
                                st.success(f"已删除视频: {selected_video}")
                                st.session_state.video_name_play = None
                                st.session_state.start_video_chat = False
                                st.session_state.conversation_id = ""
                                st.session_state.delete_confirm = False
                                st.rerun()
                            else:
                                st.error("删除失败，请重试")
                        except Exception as e:
                            st.error(f"删除过程中出错: {str(e)}")

                with confirm_col2:
                    if st.button(
                        "✖️ 取消",
                        use_container_width=True,
                        key="cancel_delete_button",
                        type="secondary",
                        args=('cancel',)  # 使用cancel类型
                    ):
                        st.session_state.delete_confirm = False
                        st.rerun()

            
    col1vc, col2vc = st.columns([1,1])
    # # 显示视频播放器和对话界面
    with col1vc:    
        if st.session_state.video_name_play and st.session_state.sent_state:           
            
            load_video(st.session_state.video_name_play,username,password,token)           
            if st.button("开始与视频对话", key="chat_button"):
                st.session_state.start_video_chat = True
                st.session_state.conversation_id = ""
                st.rerun()
    
    with col2vc:
        # 添加自定义 CSS 样式以确保对话窗口不被覆盖
        # 添加自定义 CSS 样式以确保对话窗口不被覆盖
        if st.session_state.start_video_chat:                   
            try:
                if not st.session_state.conversation_id:
                    st.session_state.conversation_id = "1"
                # 在调用 video_chat_interface 之前调用 float_parent()
                
                video_chat_interface(selected_video, 
                                     st.session_state.conversation_id,
                                     username, password, token, dataset_id)
            except Exception as e:
                print("对话创建/使用过程中出错:", str(e))
                st.error("对话初始化失败，请重试")



        st.markdown(""" 
            <style>
                .knowledge-card {
                    border: none;
                    border-radius: 20px;
                    padding: 40px; /* 增加内边距 */
                    margin-top: 20px;
                    background-color: #FFF8DC; /* 更柔和的背景色 */
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                    text-align: center;
                    margin-left: auto;
                    width: 100%; /* 使用整个容器宽度 */
                    min-width: 300px; /* 最小宽度 */
                    max-width: 700px; /* 最大宽度 */
                    transition: transform 0.3s, box-shadow 0.3s;
                    min-height: 190px; /* 增加最小高度 */
                }
                .knowledge-card:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
                }
                .knowledge-title {
                    margin: 0;
                    font-size: 2.2em; /* 增加标题字体大小 */
                    color: #FFA000; /* 使用与页面主题相匹配的颜色 */
                    text-align: center; /* 使标题居中显示 */
                    font-weight: bold; /* 加粗标题 */
                }
                .knowledge-title a {
                    text-decoration: none;
                    color: #FFA000; /* 确保链接颜色与标题一致 */
                    display: none;
                }
                .knowledge-description {
                    font-size: 1.2em; /* 增加描述字体大小 */
                    color: #2C3E50; /* 深蓝色以提高可读性 */
                    line-height: 1.5; /* 增加行高 */
                    margin-bottom: 15px; /* 增加底部间距 */
                }
                .knowledge-link { 
                    display: inline-block;
                    padding: 12px 24px;
                    background-color: #3498DB; /* 明亮的蓝色 */
                    color: #FFFFFF; /* 白色字体 */
                    border-radius: 10px;
                    text-decoration: none;
                    font-size: 1.1em;
                    transition: background-color 0.3s, transform 0.3s;
                    box-shadow: 0 2px 10px rgba(52, 152, 219, 0.2); /* 蓝色阴影 */
                }
                .knowledge-link:hover {
                    background-color: #2980B9; /* 悬停时的深蓝色 */
                    transform: scale(1.05);
                }
                .icon {
                        font-size: 1.5em; /* 图标大小 */
                        margin-right: 8px; /* 图标与文本之间的间距 */
                        color: #FFC107; /* 图标颜色 */
                    }
            </style>
        """, unsafe_allow_html=True)




    st.markdown("""
        <div class="knowledge-title"> 
            智能知识库
        </div>
    """, unsafe_allow_html=True)

    col1rag, col2rag = st.columns([1, 1])  
    with col1rag:   
        st.markdown('<div class="custom-subheader">上传文件到知识库</div>', unsafe_allow_html=True)    

    with col2rag:
        st.markdown('<div class="custom-subheader">智能知识库问答系统</div>', unsafe_allow_html=True)     



    col1vc1, col2vc1 = st.columns([1,1])
    with col1vc1:
        remaining_uploads_rag = file_upload_component(username)
        st.markdown(f'<div class="uploadInfo">根据上传文件大小，知识库至少需要5分钟向量化</div>', unsafe_allow_html=True)
    with col2vc1:
        token = get_user_token(username, config)
        knowledge_base_url = f"{origin}/api/mermindfig/chatwithfiles.html?user={username}&key={password}&token={token}"
        st.markdown(f"""
            <div class="knowledge-card">
                    <p class="knowledge-description">
                        <span class="icon">📚</span>点击下方进入智能问答页面
                    </p>
                <a href="{knowledge_base_url}" target="_blank" class="knowledge-link">
                    进入知识库
                </a>
            </div>
        """, unsafe_allow_html=True)
    st.markdown(f'<div class="uploadInfo">剩余上传次数: {remaining_uploads_rag}</div>', unsafe_allow_html=True)




    # 添加自定义 CSS 样式
    st.markdown("""
    <style>
        .usage-instructions {
            background-color: #FFF8DC;  /* 背景颜色 */
            border: 1px solid #E6B84D;  /* 边框颜色 */
            border-radius: 10px;  /* 圆角 */
            padding: 15px;  /* 内边距 */
            margin-top: 10px;  /* 上边距 */
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);  /* 阴影效果 */
            color: #5A4A1C;  /* 文字颜色 */
            font-size: 0.95em;  /* 字体大小 */
        }
    </style>
    """, unsafe_allow_html=True)

    # 使用说明
    st.markdown("""
    <div class="usage-instructions">
        <strong>使用说明：</strong><br>        
        1. <strong>启用深度搜索</strong>：勾选此框将启用深度搜索功能。会消耗更多的token<br>
        2. <strong>功能影响</strong>：当您选择启用深度搜索时，系统将根据您选择的搜索方法（支持"本地"方法）进行更深入的搜索和查询。<br>
        3. <strong>参数设置</strong>：输出语言会和查询语言一致
    </div>
    """, unsafe_allow_html=True)

    conversation_id_model_1=""

    user_config = {
        'api_key_deepseek': api_key_deepseek,
        'api_key_siliconflow': api_key_siliconflow,
        'api_key_qwen': api_key_qwen,
        'api_key_type': api_key_type,
        'conversation_id': conversation_id_model_1,
        'api_key_rag': api_key_rag,
    }

    save_user_config(username, user_config)





################################################################################################################################################


def send_verification_email(email, code):
    smtp_server = "smtp.163.com"
    smtp_port = 465  # 或者使用25，如果你的网络提供商没有封锁25端口
    sender_email = "<EMAIL>"  # 替换为你的163邮箱地址
    sender_password = "BHtZW6UT2kv6YR2s"  # 替换为你设置的客户端授权密码

    message = MIMEMultipart("alternative")
    message["Subject"] = "验证码"
    message["From"] = sender_email
    message["To"] = email

    text = f"您的验证码是：{code}"
    part = MIMEText(text, "plain")
    message.attach(part)

    try:
        with smtplib.SMTP_SSL(smtp_server, smtp_port) as server:  # 注意这里使用SMTP_SSL
            server.login(sender_email, sender_password)
            server.sendmail(sender_email, email, message.as_string())
        print("邮件发送成功")
        return True
    except Exception as e:
        print(f"发送邮件时出错: {str(e)}")
        return False

def is_username_taken(username, config):
    return username in config['credentials']['usernames']

def is_email_taken(email, config):
    return any(user['email'] == email for user in config['credentials']['usernames'].values())




# Streamlit 主程序
def main(): 

    top_container = st.container()

    with top_container:
        st.markdown("""
        <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap');
        
        /* 全局样式 */
        .stApp {
            background-color: #FFFBE6;  /* 淡黄色背景 */
        }
        .stApp > header {
            background-color: transparent;
        }                    
        /* 登录标题样式 */
        .login-title-container {
            text-align: center;
            padding: 20px 0;
            margin-bottom: 20px;
            pointer-events: none;  /* 禁用点击事件 */
        }
        
        .login-title {
            color: #5A4A1C;
            font-family: 'Roboto', sans-serif;
            font-size: 1.8em;
            font-weight: 500;
            margin: 0;
            padding: 10px 30px;
            display: inline-block;
            position: relative;
            background: linear-gradient(120deg, #FFF8DC 0%, #F3E2A9 100%);
            border-radius: 10px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.05);
            border: 1px solid #F3E2A9;
            text-decoration: none;  /* 移除下划线 */
            cursor: default;  /* 移除指针样式 */
        }
        
        .login-title span {
            background: linear-gradient(120deg, #8B7355 0%, #5A4A1C 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            z-index: 1;
            pointer-events: none;  /* 禁用点击事件 */
        }
        
        /* 确保标题内的链接样式被覆盖 */
        .login-title-container a,
        .login-title-container a:hover,
        .login-title-container a:visited,
        .login-title-container a:active {
            text-decoration: none;
            color: inherit;
            pointer-events: none;
        }

        .user-welcome {
            padding: 1rem;
            background: linear-gradient(135deg, #F0F5F0, #E8F0E8);
            border-radius: 10px;
            border: 1px solid #D7E3D7;
            margin: 1rem 0;
            text-align: center;
        }
        
        .user-welcome-text {
            color: #2C3E2C;
            font-size: 1.1em;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        
        .user-name {
            color: #4A6B4A;
            font-size: 1.2em;
            font-weight: bold;
            margin: 0.5rem 0;
        }
        
        .logout-button {
            margin-top: 1rem;
        }
        
        /* 分割线样式 */
        .sidebar-divider {
            margin: 1rem 0;
            border-top: 1px solid #E0E8E0;
        }        
        /* 表单容器样式 */
        .stForm {
            background-color: #FFF8DC;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid #F3E2A9;
        }
        
        /* 输入框样式 */
        .stTextInput > div > div > input {
            background-color: #FFFDF0;
            border: 1px solid #F3E2A9;
            border-radius: 5px;
            padding: 0.75rem;  /* 增加内边距 */
            font-size: 16px;
            min-height: 44px;  /* 确保在移动设备上有足够的点击区域 */
            color: #2C3E2C;  /* 确保文字颜色足够明显 */
            opacity: 1 !important;  /* 确保输入内容可见 */
            -webkit-text-fill-color: #2C3E2C !important;  /* 针对iOS设备 */
        }
        /* 输入框获得焦点时的样式 */
        .stTextInput > div > div > input:focus {
            box-shadow: 0 0 0 2px rgba(240,198,116,0.5);
            border-color: #F0C674;
        }           
        /* 标题样式 */
        .stMarkdown h3 {
            color: #5A4A1C;
            font-family: 'Roboto', sans-serif;
            font-weight: 500;
            margin-bottom: 1.5rem;
        }
        
        /* 消息样式 */
        .stSuccess {
            background-color: #E8F0E0;
            color: #2C3E2C;
            border: 1px solid #D7E3D7;
            padding: 0.75rem;
            border-radius: 5px;
        }
        
        .stError {
            background-color: #FFE8E6;
            color: #8B3E38;
            border: 1px solid #F3D1D0;
            padding: 0.75rem;
            border-radius: 5px;
        }
        
        /* 单选按钮样式 */
        .stRadio > div {
            background-color: #FFF8DC;
            padding: 1rem;
            border-radius: 10px;
            border: 1px solid #F3E2A9;
        }
        
        /* 侧边栏样式 */
        .css-1d391kg {
            background-color: #FFF8DC;
        }
        
        /* 登录表单容器 */
        .login-container {
            max-width: 450px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .scrolling-text-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: linear-gradient(90deg, rgba(240,240,240,0.5) 0%, rgba(255,255,255,0.6) 50%, rgba(240,240,240,0.5) 100%);
            padding: 12px 0;
            overflow: hidden;
            z-index: 999999;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            pointer-events: none;  /* 允许点击穿透 */
        }
        .scrolling-text {
            display: inline-block;
            white-space: nowrap;
            animation: scroll 30s linear infinite;
            font-weight: bold;
            font-family: 'Roboto', sans-serif;
            font-size: 16px;
            color: #333;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }
        @keyframes scroll {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        /* 适配移动设备的响应式样式 */
        @media (max-width: 768px) {
            .stForm {
                padding: 1rem;  /* 减小表单内边距 */
            }
            
            .stTextInput > div > div > input {
                font-size: 16px;  /* 确保字体大小适合移动设备 */
                padding: 0.5rem;  /* 调整内边距 */
            }
            
            /* 确保密码输入框在移动设备上正常显示 */
            input[type="password"] {
                -webkit-text-security: disc !important;
                text-security: disc !important;
            }
        }
        
        /* 修复iOS设备上的输入框问题 */
        input, textarea {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }
        
        /* 确保输入框文字在深色和浅色模式下都清晰可见 */
        .stTextInput > div > div > input::placeholder {
            color: #8B7355;
            opacity: 0.7;
        }                   
        </style>
        <div class="scrolling-text-container">
            <div class="scrolling-text">
                🕒 网页开放时间：9:00 - 02:00 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                🕒 网页开放时间：9:00 - 02:00 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                🕒 网页开放时间：9:00 - 02:00 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </div>
        </div>
        """, unsafe_allow_html=True)



     # 检查是否已经执行过重置操作

    reset_user_counts()

    config = load_config()
    
    # 将配置文件中的明文密码转换为哈希密码
    config = convert_plaintext_passwords_to_hash(config)

    # 初始化 session_state
    if 'authentication_status' not in st.session_state:
        st.session_state.authentication_status = False
    if 'username' not in st.session_state:
        st.session_state.username = None
    if 'verification_code' not in st.session_state:
        st.session_state.verification_code = None
    if 'email' not in st.session_state:
        st.session_state.email = None
    if 'show_login' not in st.session_state:
        st.session_state.show_login = False
    if 'username_valid' not in st.session_state:
        st.session_state.username_valid = False
    if 'email_valid' not in st.session_state:
        st.session_state.email_valid = False
    if 'page' not in st.session_state:
        st.session_state.page = "main"




    if not st.session_state.authentication_status:
        # 创建居中的容器
        col1, col2, col3 = st.columns([1, 1, 1])
        
        with col2:
            if st.session_state.show_login:
                auth_status = 'Login'
            else:
                auth_status = st.radio(
                    label="认证选项",  # 添加有意义的label
                    options=['登录', '注册', '找回密码'],
                    horizontal=True,
                    label_visibility="collapsed"  # 隐藏label但保持可访问性
                )

            # 登录表单
            if auth_status in ['Login', '登录']:
                with st.form("login_form"):
                    st.markdown("""
                        <div class="login-title-container">
                            <h3 class="login-title">
                                <span>✨欢迎登录✨</span>
                            </h3>
                        </div>
                    """, unsafe_allow_html=True)
                    username = st.text_input("用户名", placeholder="请输入用户名")
                    password = st.text_input("密码", type="password", placeholder="请输入密码")
                    col1, col2, col3 = st.columns([1,1,1])
                    with col2:
                        submit_button = st.form_submit_button("登 录")

                if submit_button:
                    if username and password:  # 确保输入不为空
                        success = login(username, password, config)
                        if success:
                            st.session_state.authentication_status = True
                            st.session_state.username = username
                            st.success("登录成功！")
                            time.sleep(1)  # 短暂延迟以显示成功消息
                            refresh_user_token_on_login(username, config)
                            st.rerun()
                        else:
                            st.error('用户名或密码错误')
                    else:
                        st.warning('请输入用户名和密码')

            # 注册表单
            elif auth_status in ['Register', '注册']:
                with st.form("register_form"):
                    st.markdown("<h3 style='text-align: center'>新用户注册</h3>", unsafe_allow_html=True)
                    new_username = st.text_input("用户名", placeholder="设置用户名")
                    new_password = st.text_input("密码", type="password", placeholder="设置密码")
                    new_password_repeat = st.text_input("确认密码", type="password", placeholder="再次输入密码")
                    email = st.text_input("邮箱", placeholder="请输入有效邮箱")
                    col1, col2, col3 = st.columns([1,1,1])
                    with col2:
                        submit_button = st.form_submit_button("发送验证码")

                # 用户名和邮箱验证
                if new_username:
                    if is_username_taken(new_username, config):
                        st.error("该用户名已被使用")
                        st.session_state.username_valid = False
                    else:
                        st.success("用户名可用")
                        st.session_state.username_valid = True

                if email:
                    if is_email_taken(email, config):
                        st.error("该邮箱已被注册")
                        st.session_state.email_valid = False
                    else:
                        st.success("邮箱可用")
                        st.session_state.email_valid = True

                # 处理验证码发送
                if submit_button:
                    if not new_username or not new_password or not new_password_repeat or not email:
                        st.error("请填写所有必填项")
                    elif not st.session_state.username_valid or not st.session_state.email_valid:
                        st.error("请确保用户名和邮箱都有效")
                    elif new_password != new_password_repeat:
                        st.error("两次输入的密码不匹配")
                    elif len(new_password) < 6:
                        st.error("密码长度至少为6位")
                    else:
                        # 生成并发送验证码
                        verification_code = ''.join([str(random.randint(0, 9)) for _ in range(5)])
                        st.session_state.verification_code = verification_code
                        st.session_state.email = email
                        
                        # 保存注册信息
                        st.session_state.register_info = {
                            'username': new_username,
                            'password': new_password,
                            'email': email
                        }

                        if send_verification_email(email, verification_code):
                            st.success("验证码已发送到您的邮箱，请查收")
                        else:
                            st.error("发送验证码失败，请检查邮箱地址或稍后重试")

                # 验证码确认表单
                if st.session_state.verification_code:
                    with st.form("verification_form"):
                        st.markdown("<h3 style='text-align: center'>邮箱验证</h3>", unsafe_allow_html=True)
                        entered_code = st.text_input("验证码", placeholder="请输入收到的验证码")
                        col1, col2, col3 = st.columns([1,1,1])
                        with col2:
                            final_submit = st.form_submit_button("完成注册")

                    if final_submit:
                        if entered_code == st.session_state.verification_code:
                            reg_info = st.session_state.register_info
                            success, message = register(
                                reg_info['username'],
                                reg_info['password'],
                                reg_info['email'],
                                config
                            )
                            if success:
                                st.success(f"恭喜 {reg_info['username']} 注册成功！请登录。")
                                # 清除所有注册相关的session状态
                                for key in ['verification_code', 'email', 'register_info', 'username_valid', 'email_valid']:
                                    if key in st.session_state:
                                        del st.session_state[key]
                                st.session_state.show_login = True
                                time.sleep(1)
                                st.rerun()
                            else:
                                st.error(message)
                        else:
                            st.error("验证码不正确")


            # 找回密码表单
            elif auth_status == '找回密码':
                if 'reset_stage' not in st.session_state:
                    st.session_state.reset_stage = 'email'

                if st.session_state.reset_stage == 'email':
                    with st.form("reset_password_form"):
                        st.markdown("<h3 style='text-align: center'>找回密码</h3>", unsafe_allow_html=True)
                        reset_email = st.text_input("注册邮箱", placeholder="请输入注册时使用的邮箱")
                        col1, col2, col3 = st.columns([1,1,1])
                        with col2:
                            submit_button = st.form_submit_button("提交")

                    if submit_button and reset_email:
                        # 查找邮箱对应的用户
                        found_users = []
                        for username, user_data in config['credentials']['usernames'].items():
                            if user_data['email'] == reset_email:
                                found_users.append(username)
                        
                        if found_users:
                            # 生成验证码
                            reset_code = ''.join([str(random.randint(0, 9)) for _ in range(5)])
                            st.session_state.reset_code = reset_code
                            st.session_state.reset_email = reset_email
                            st.session_state.reset_users = found_users

                            # 发送包含用户名和验证码的邮件
                            email_content = f"""
                            您的账号信息：
                            用户名：{', '.join(found_users)}
                            
                            重置密码验证码：{reset_code}
                            
                            请使用此验证码重置您的密码。
                            """
                            
                            if send_verification_email(reset_email, email_content):
                                st.success("验证码已发送到您的邮箱，请查收")
                                st.session_state.reset_stage = 'verify'
                            else:
                                st.error("发送验证码失败，请稍后重试")
                        else:
                            st.error("该邮箱未注册")

                if st.session_state.reset_stage == 'verify':  # 修改条件判断
                    with st.form("verify_reset_code"):
                        st.markdown("<h3 style='text-align: center'>验证身份</h3>", unsafe_allow_html=True)
                        if len(st.session_state.reset_users) > 1:
                            selected_user = st.selectbox("选择要重置的用户名", st.session_state.reset_users)
                        else:
                            selected_user = st.session_state.reset_users[0]
                            st.info(f"用户名：{selected_user}")
                        
                        entered_code = st.text_input("验证码", placeholder="请输入收到的验证码")
                        new_password = st.text_input("新密码", type="password", placeholder="请输入新密码")
                        confirm_password = st.text_input("确认新密码", type="password", placeholder="请再次输入新密码")
                        
                        col1, col2, col3 = st.columns([1,1,1])
                        with col2:
                            submit_button = st.form_submit_button("重置密码")

                    if submit_button:
                        if entered_code != st.session_state.reset_code:
                            st.error("验证码错误")
                        elif len(new_password) < 6:
                            st.error("密码长度至少为6位")
                        elif new_password != confirm_password:
                            st.error("两次输入的密码不匹配")
                        else:
                            # 更新密码
                            hashed_password = bcrypt.hashpw(new_password.encode(), bcrypt.gensalt())
                            config['credentials']['usernames'][selected_user]['password'] = hashed_password.decode()
                            
                            # 保存更新后的配置
                            with open('config.yaml', 'w', encoding='utf-8') as file:
                                yaml.dump(config, file, allow_unicode=True)
                            
                            st.success("密码重置成功！请使用新密码登录")
                            # 清除重置相关的session状态
                            for key in ['reset_stage', 'reset_code', 'reset_email', 'reset_users']:
                                if key in st.session_state:
                                    del st.session_state[key]
                            st.session_state.show_login = True
                            time.sleep(1)
                            st.rerun()








    else:
        # 用户已登录状态

        # 在侧边栏添加欢迎信息
         with st.sidebar:
            # 检查用户的 VIP 状态
            is_vip = False
            vip_days_left = None
            token = get_user_token(st.session_state.username, config)
            print('token is:',token) 
            token_expiry_time=get_user_expiry(st.session_state.username, config)
            print('token expiry time is:',token_expiry_time)
            # 从配置文件中读取用户信息
            with open('config.yaml', 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                
            if st.session_state.username in config['credentials']['usernames']:
                user_info = config['credentials']['usernames'][st.session_state.username]
                if 'vip_expiry' in user_info:
                    expiry_timestamp = user_info['vip_expiry']
                    current_time = time.time()
                    if expiry_timestamp > current_time:
                        is_vip = True
                        days_left = (expiry_timestamp - current_time) / (24 * 3600)
                        vip_days_left = int(days_left)

            # 根据 VIP 状态显示不同的欢迎信息
            if is_vip:
                st.markdown(f"""
                    <style>
                        @keyframes shine {{
                            0% {{
                                background-position: -100% center;
                            }}
                            100% {{
                                background-position: 200% center;
                            }}
                        }}
                        
                        .vip-username {{
                            background: linear-gradient(
                                90deg, 
                                #FF8C00 0%,  /* 深橙色 */
                                #FFD700 20%, /* 金色 */
                                #FFA500 40%, /* 橙色 */
                                #FFD700 60%, /* 金色 */
                                #FF8C00 80%, /* 深橙色 */
                                #FFD700 100% /* 金色 */
                            );
                            background-size: 200% auto;
                            color: transparent;
                            -webkit-background-clip: text;
                            background-clip: text;
                            animation: shine 3s linear infinite;
                            font-weight: bold;
                            font-size: 1.3em;
                            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
                            padding: 0 5px;
                            letter-spacing: 1px;
                        }}
                    </style>
                    <div class="user-welcome">
                        <div class="user-welcome-text">
                            <span style="font-size: 1.2em; color: #FFD700;">👑</span> 
                            尊敬的会员，欢迎回来
                        </div>
                        <div class="user-name" style="font-size: 1.2em;
                                                    margin: 8px 0;
                                                    display: flex;
                                                    align-items: center;
                                                    gap: 10px;">
                            <span class="vip-username">{st.session_state.username}</span>
                            <span style="background: linear-gradient(45deg, #FF8C00, #FFD700);
                                       color: #000; 
                                       padding: 4px 12px; 
                                       border-radius: 15px; 
                                       font-size: 0.8em;
                                       box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                                       display: inline-flex;
                                       align-items: center;
                                       gap: 4px;">
                                <span style="color: #8B4513;">♛</span>
                                VIP {vip_days_left} 天
                            </span>
                        </div>
                    </div>
                    <div class="sidebar-divider"></div>
                """, unsafe_allow_html=True)

            else:
                st.markdown("""
                    <div class="user-welcome">
                        <div class="user-welcome-text">👋 欢迎回来</div>
                        <div class="user-name">%s</div>
                    </div>
                    <div class="sidebar-divider"></div>
                """ % st.session_state.username, unsafe_allow_html=True)                     
            # 将退出按钮放在侧边栏底部
            st.markdown("<div class='logout-button'>", unsafe_allow_html=True)
            if st.button('🚪 退出登录', use_container_width=True,        
                         key="logout",
                         type="primary",
                         args=('confirm',) ):
                for key in st.session_state.keys():
                    del st.session_state[key]
                st.rerun()
            st.markdown("</div>", unsafe_allow_html=True)

            # 显示主页面 
            st.markdown("""
                <style>
                    /* 设置整个页面的最小宽度 */
                    .stApp {
                        min-width: 400px;  /* 设置最小宽度为800px，可以根据需要调整 */
                    }
                </style>
            """, unsafe_allow_html=True)             
         display_main_page()



    # 移除侧边栏的聊天记录查看器入口（如有）




if __name__ == "__main__":
    main()

