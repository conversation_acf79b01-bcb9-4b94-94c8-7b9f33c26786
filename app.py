from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, send_from_directory
import yaml
import bcrypt
import json
import os
import time
import secrets
import random
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import requests
from datetime import datetime
import logging
from dashboard_api import register_dashboard_routes
from logger_config import video_server_logger as logger, CustomTransLogger
# 获取当前文件的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 设置模板目录为 main/templates
template_dir = os.path.join(current_dir, 'main', 'templates')
# 设置静态文件目录为 main/static
static_dir = os.path.join(current_dir, 'main', 'static')
# 访问记录文件路径
ACCESS_LOG_FILE = 'access_log.json'
# 创建蓝图
main_bp = Blueprint('main', __name__, 
                   template_folder=template_dir,
                   static_folder=static_dir,
                   static_url_path='/main/static')

# 配置文件路径 - 相对于项目根目录（app.py的上一级目录）
CONFIG_FILE = 'config.yaml'
USER_CONFIG_FILE = 'user_configs.json'
USER_FILES_JSON = 'user/user_files.json'
DOC_IDS_JSON = 'ragdoc/doc_ids.json'
USER_RAG_COUNTS_FILE = 'user_rag_counts.json'

# API配置
BASE_URL = "http://localhost:8080/v1/api"
API_KEY_MODEL = "ragflow-Y1NTkyNDBlZmZhNTExZWY5ZmNhZWUzZD"
API_KEY_DEEPSEEK = "***********************************"
API_KEY_SILICONFLOW = "sk-izwfqiufrwdqfkvqzklcyaashpmraqaaosuchkjnettqpabb"
API_KEY_QWEN = "sk-61010d52e30c4fb096d240ad7fae39df"

# 上传限制
MAX_RAG_UPLOADS = 2
MAX_VIDEO_UPLOADS = 1
VIP_EXTRA_RAG_UPLOADS = 10
VIP_EXTRA_VIDEO_UPLOADS = 10

# 允许的文件类型
ALLOWED_EXTENSIONS = ['.docx', '.xlsx', '.pptx', '.jpg', '.jpeg', '.png', '.pdf', '.txt', '.md', '.json', '.eml']

def load_config():
    """加载配置文件"""
    if not os.path.exists(CONFIG_FILE):
        return get_default_config()
    with open(CONFIG_FILE, 'r', encoding='utf-8') as file:
        return yaml.safe_load(file)

def get_default_config():
    """获取默认配置"""
    return {
        'credentials': {
            'usernames': {}
        },
        'cookie': {
            'expiry_days': 30,
            'key': 'some_signature_key',
            'name': 'some_cookie_name'
        },
        'preauthorized': {
            'emails': []
        }
    }

def save_config(config):
    """保存配置文件"""
    with open(CONFIG_FILE, 'w', encoding='utf-8') as file:
        yaml.dump(config, file, default_flow_style=False, allow_unicode=True)

def verify_password(stored_password, provided_password):
    """验证密码"""
    try:
        if isinstance(stored_password, int):
            stored_password = str(stored_password)
        if isinstance(provided_password, int):
            provided_password = str(provided_password)
        
        if not str(stored_password).startswith('$2'):
            return str(stored_password) == str(provided_password)
        
        return bcrypt.checkpw(provided_password.encode(), stored_password.encode())
    except Exception as e:
        print(f"Password verification error: {e}")
        return str(stored_password) == str(provided_password)

def hash_password(password):
    """哈希密码"""
    if not isinstance(password, str):
        password = str(password)
    return bcrypt.hashpw(password.encode(), bcrypt.gensalt()).decode()

def send_verification_email(email, code):
    """发送验证码邮件"""
    smtp_server = "smtp.163.com"
    smtp_port = 465
    sender_email = "<EMAIL>"
    sender_password = "BHtZW6UT2kv6YR2s"

    message = MIMEMultipart("alternative")
    message["Subject"] = "验证码"
    message["From"] = sender_email
    message["To"] = email

    text = f"您的验证码是：{code}"
    part = MIMEText(text, "plain")
    message.attach(part)

    try:
        with smtplib.SMTP_SSL(smtp_server, smtp_port) as server:
            server.login(sender_email, sender_password)
            server.sendmail(sender_email, email, message.as_string())
        return True
    except Exception as e:
        print(f"发送邮件时出错: {str(e)}")
        return False

def generate_access_token():
    """生成访问令牌"""
    return secrets.token_urlsafe(32)

def update_user_token(username, config):
    """更新用户令牌"""
    token = generate_access_token()
    expiry = time.time() + 720 * 3600  # 720小时（30天）后过期
    
    config['credentials']['usernames'][username]['token'] = token
    config['credentials']['usernames'][username]['expiry'] = expiry
    
    save_config(config)
    return token

def get_user_token(username, config):
    """获取用户令牌"""
    user_info = config['credentials']['usernames'][username]
    
    if 'token' not in user_info or 'expiry' not in user_info:
        return update_user_token(username, config)
    
    current_time = time.time()
    if current_time > user_info['expiry']:
        return update_user_token(username, config)
    
    return user_info['token']

def get_user_upload_limits(username):
    """获取用户上传限制"""
    limits = {
        'rag': MAX_RAG_UPLOADS,
        'video': MAX_VIDEO_UPLOADS
    }
    
    try:
        config = load_config()
        if username == "admin":
            return {
                'rag': float('inf'),
                'video': float('inf')
            }
            
        if username in config['credentials']['usernames']:
            user_info = config['credentials']['usernames'][username]
            if 'vip_expiry' in user_info:
                expiry_timestamp = user_info['vip_expiry']
                if expiry_timestamp > time.time():
                    limits['rag'] += VIP_EXTRA_RAG_UPLOADS
                    limits['video'] += VIP_EXTRA_VIDEO_UPLOADS
    except Exception as e:
        logging.error(f"Error checking VIP status for upload limits: {str(e)}")
    
    return limits




def manage_user_rag_count(username, increment=False):
    """管理用户RAG上传次数"""
    if not os.path.exists(USER_RAG_COUNTS_FILE):
        with open(USER_RAG_COUNTS_FILE, 'w') as f:
            json.dump({}, f, ensure_ascii=False)
    
    with open(USER_RAG_COUNTS_FILE, 'r') as f:
        counts = json.load(f)
    
    if username not in counts:
        counts[username] = 0
    
    if increment:
        counts[username] += 1
        with open(USER_RAG_COUNTS_FILE, 'w') as f:
            json.dump(counts, f, ensure_ascii=False)
    
    return counts[username]

# 主应用辅助函数
def load_main_config():
    """加载主应用配置文件"""
    if not os.path.exists(CONFIG_FILE):
        return get_default_main_config()
    with open(CONFIG_FILE, 'r', encoding='utf-8') as file:
        return yaml.safe_load(file)

def get_default_main_config():
    """获取默认配置"""
    return {
        'credentials': {
            'usernames': {}
        }
    }

def load_access_log():
    """加载访问记录"""
    if not os.path.exists(ACCESS_LOG_FILE):
        return {
            'total_visits': 0,
            'user_visits': {}
        }
    try:
        with open(ACCESS_LOG_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Error loading access log: {str(e)}")
        return {
            'total_visits': 0,
            'user_visits': {}
        }
    




def record_root_access(username=None):
    """记录根路径访问"""
    try:
        access_data = load_access_log()
        current_time = datetime.now()
        current_time_str = current_time.isoformat()

        # 防重复记录：检查是否在5秒内有相同用户的访问记录
        should_record = True
        user_key = username or 'anonymous'

        if user_key in access_data['user_visits']:
            last_visit_str = access_data['user_visits'][user_key]['last_visit']
            try:
                last_visit_time = datetime.fromisoformat(last_visit_str)
                time_diff = (current_time - last_visit_time).total_seconds()
                if time_diff < 5:  # 5秒内的重复访问不记录
                    should_record = False
                    logger.info(f"Skipped duplicate access for user: {user_key} (within 5 seconds)")
            except ValueError:
                # 如果时间格式解析失败，继续记录
                pass

        if should_record:
            # 增加总访问次数
            access_data['total_visits'] += 1

            # 记录用户访问信息
            if username:
                if username not in access_data['user_visits']:
                    access_data['user_visits'][username] = {
                        'count': 0,
                        'first_visit': current_time_str,
                        'last_visit': current_time_str
                    }

                access_data['user_visits'][username]['count'] += 1
                access_data['user_visits'][username]['last_visit'] = current_time_str
            else:
                # 匿名访问
                if 'anonymous' not in access_data['user_visits']:
                    access_data['user_visits']['anonymous'] = {
                        'count': 0,
                        'first_visit': current_time_str,
                        'last_visit': current_time_str
                    }

                access_data['user_visits']['anonymous']['count'] += 1
                access_data['user_visits']['anonymous']['last_visit'] = current_time_str

            save_access_log(access_data)
            logger.info(f"Recorded root access for user: {username or 'anonymous'}")

    except Exception as e:
        logger.error(f"Error recording root access: {str(e)}")


def save_access_log(access_data):
    """保存访问记录"""
    try:
        with open(ACCESS_LOG_FILE, 'w', encoding='utf-8') as f:
            json.dump(access_data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logger.error(f"Error saving access log: {str(e)}")






@main_bp.route('/')
def index():
    """主页面"""
    if 'username' in session:
        return redirect(url_for('main.dashboard'))
    return render_template('index.html')



@main_bp.route('/logout')
def logout():
    """登出"""
    session.clear()
    return redirect(url_for('main.index'))


@main_bp.route('/dashboard')
def dashboard():
    """仪表板页面"""
    if 'username' not in session:
        return redirect(url_for('main.index'))

    username = session['username']
    config = load_main_config()
    # 记录访问
    record_root_access(username)
    # 检查VIP状态
    is_vip = False
    vip_days_left = 0
    if username in config['credentials']['usernames']:
        user_info = config['credentials']['usernames'][username]
        if 'vip_expiry' in user_info:
            expiry_timestamp = user_info['vip_expiry']
            if expiry_timestamp > time.time():  # VIP 未过期
                is_vip = True
                vip_days_left = int((expiry_timestamp - time.time()) / (24 * 3600))

    # 获取上传限制
    limits = get_user_upload_limits(username)
    current_uploads = manage_user_rag_count(username)
    remaining_uploads = limits['rag'] - current_uploads

    return render_template('dashboard.html',
                        username=username,
                        is_vip=is_vip,
                        vip_days_left=vip_days_left,
                        remaining_uploads=remaining_uploads)




@main_bp.route('/login', methods=['POST'])
def login():
    """登录接口"""
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    if not username or not password:
        return jsonify({'success': False, 'message': '请输入用户名和密码'})
    
    config = load_config()
    
    if username in config['credentials']['usernames']:
        user_info = config['credentials']['usernames'][username]
        stored_password = user_info['password']
        
        if verify_password(stored_password, password):
            session.permanent = True  # 设置session为永久
            session['username'] = username
            session['authenticated'] = True
            # 更新用户令牌
            token = get_user_token(username, config)
            session['token'] = token

            return jsonify({'success': True, 'message': '登录成功'})
    
    return jsonify({'success': False, 'message': '用户名或密码错误'})


@main_bp.route('/register', methods=['POST'])
def register():
    """注册接口"""
    data = request.get_json()
    action = data.get('action')
    
    if action == 'send_code':
        username = data.get('username')
        password = data.get('password')
        email = data.get('email')
        
        if not all([username, password, email]):
            return jsonify({'success': False, 'message': '请填写所有必填项'})
        
        config = load_config()
        
        # 检查用户名是否已存在
        if username in config['credentials']['usernames']:
            return jsonify({'success': False, 'message': '该用户名已被使用'})
        
        # 检查邮箱是否已注册
        for user_data in config['credentials']['usernames'].values():
            if user_data.get('email') == email:
                return jsonify({'success': False, 'message': '该邮箱已被注册'})
        
        if len(password) < 6:
            return jsonify({'success': False, 'message': '密码长度至少为6位'})
        
        # 生成验证码
        verification_code = ''.join([str(random.randint(0, 9)) for _ in range(5)])
        
        # 保存注册信息到session
        session['register_info'] = {
            'username': username,
            'password': password,
            'email': email,
            'verification_code': verification_code
        }
        
        # 发送验证码
        if send_verification_email(email, verification_code):
            return jsonify({'success': True, 'message': '验证码已发送到您的邮箱'})
        else:
            return jsonify({'success': False, 'message': '发送验证码失败'})
    
    elif action == 'verify_code':
        code = data.get('code')
        
        if 'register_info' not in session:
            return jsonify({'success': False, 'message': '注册信息已过期，请重新注册'})
        
        register_info = session['register_info']
        
        if code != register_info['verification_code']:
            return jsonify({'success': False, 'message': '验证码不正确'})
        
        # 创建用户
        config = load_config()
        hashed_password = hash_password(register_info['password'])
        
        config['credentials']['usernames'][register_info['username']] = {
            'password': hashed_password,
            'email': register_info['email']
        }
        
        save_config(config)
        
        # 清除session中的注册信息
        session.pop('register_info', None)
        
        return jsonify({'success': True, 'message': '注册成功！请登录'})
    
    return jsonify({'success': False, 'message': '无效的操作'})





@main_bp.route('/check-username', methods=['POST'])
def check_username():
    """检查用户名是否可用"""
    data = request.get_json()
    username = data.get('username')

    if not username:
        return jsonify({'success': False, 'message': '用户名不能为空'})

    config = load_config()
    available = username not in config['credentials']['usernames']

    return jsonify({'success': True, 'available': available})

@main_bp.route('/check-email', methods=['POST'])
def check_email():
    """检查邮箱是否可用"""
    data = request.get_json()
    email = data.get('email')

    if not email:
        return jsonify({'success': False, 'message': '邮箱不能为空'})

    config = load_config()
    available = True

    for user_data in config['credentials']['usernames'].values():
        if user_data.get('email') == email:
            available = False
            break

    return jsonify({'success': True, 'available': available})



def update_user_files(username, filename):
    """更新用户文件记录"""
    os.makedirs(os.path.dirname(USER_FILES_JSON), exist_ok=True)
    user_files = {}

    if os.path.exists(USER_FILES_JSON):
        try:
            with open(USER_FILES_JSON, 'r', encoding='utf-8') as f:
                content = f.read()
                if content:
                    user_files = json.loads(content)
        except (json.JSONDecodeError, Exception) as e:
            print(f"读取 {USER_FILES_JSON} 时发生错误: {str(e)}")

    if username not in user_files:
        user_files[username] = []

    if filename not in user_files[username]:
        user_files[username].append(filename)

    try:
        with open(USER_FILES_JSON, 'w', encoding='utf-8') as f:
            json.dump(user_files, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"写入 {USER_FILES_JSON} 时发生错误: {str(e)}")



@main_bp.route('/reset-password', methods=['POST'])
def reset_password():
    """找回密码接口"""
    data = request.get_json()
    action = data.get('action')

    if action == 'find_user':
        email = data.get('email')

        if not email:
            return jsonify({'success': False, 'message': '请输入邮箱地址'})

        config = load_config()

        # 查找邮箱对应的用户
        found_user = None
        for username, user_data in config['credentials']['usernames'].items():
            if user_data.get('email') == email:
                found_user = username
                break

        if not found_user:
            return jsonify({'success': False, 'message': '该邮箱未注册'})

        # 生成验证码
        verification_code = ''.join([str(random.randint(0, 9)) for _ in range(5)])

        # 保存重置密码信息到session
        session['reset_info'] = {
            'username': found_user,
            'email': email,
            'verification_code': verification_code
        }

        # 发送验证码
        if send_verification_email(email, verification_code):
            return jsonify({
                'success': True,
                'message': '验证码已发送到您的邮箱',
                'username': found_user
            })
        else:
            return jsonify({'success': False, 'message': '发送验证码失败'})

    elif action == 'verify_and_reset':
        code = data.get('code')
        new_password = data.get('new_password')

        if not code or not new_password:
            return jsonify({'success': False, 'message': '请填写所有必填项'})

        if 'reset_info' not in session:
            return jsonify({'success': False, 'message': '重置信息已过期，请重新操作'})

        reset_info = session['reset_info']

        if code != reset_info['verification_code']:
            return jsonify({'success': False, 'message': '验证码不正确'})

        if len(new_password) < 6:
            return jsonify({'success': False, 'message': '密码长度至少为6位'})

        # 更新用户密码
        config = load_config()
        hashed_password = hash_password(new_password)
        config['credentials']['usernames'][reset_info['username']]['password'] = hashed_password
        save_config(config)

        # 清除session中的重置信息
        session.pop('reset_info', None)

        return jsonify({'success': True, 'message': '密码重置成功！请使用新密码登录'})

    return jsonify({'success': False, 'message': '无效的操作'})



def init_app(app):
    """初始化函数，用于注册蓝图和配置"""
    app.secret_key = 'your-secret-key-here'
    register_dashboard_routes(app)
    # 创建必要的目录
    os.makedirs('user', exist_ok=True)
    os.makedirs('ragdoc', exist_ok=True)
    return app
